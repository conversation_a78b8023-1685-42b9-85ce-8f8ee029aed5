{{>php_file_header}}

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * CreateBonusRequest
 */
class CreateBonusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'type' => [
                'required',
                'string',
                Rule::in([
                    'welcome',
                    'welcome_4_steps',
                    'onetime',
                    'no_dep',
                    'deposit',
                    'freespin_bonus',
                    'free_spins_for_deposit',
                    'wager',
                    'freebet',
                    'free_money',
                    'bet_refund',
                    'no_risk'
                ])
            ],
            'weight' => 'required|numeric|min:1|max:999',
            'image' => 'nullable|string|url',
            'description' => 'nullable|array',
            'condition' => 'nullable|array',
            'bonus_name' => 'nullable|array',
            'min_bet' => 'nullable|integer|min:1',
            'min_deposit' => 'nullable|integer|min:1',
            'max_transfers' => 'nullable|array',
            'max_transfers.*' => 'integer',
            'max_bonuses' => 'nullable|array',
            'max_bonuses.*' => 'integer',
            'deposit_factors' => 'nullable|array',
            'deposit_factors.*' => 'integer',
            'duration' => 'nullable|integer',
            'wager' => 'nullable|numeric',
            'currency' => 'nullable|string|max:3',
            'active' => 'nullable|boolean',
            'casino' => 'nullable|boolean',
            'bets' => 'nullable|boolean',
            'from' => 'nullable|string',
            'to' => 'nullable|string',
            'min_factor' => 'nullable|numeric',
            'data' => 'nullable|array',
            'providers_ids' => 'nullable|array',
            'providers_ids.*' => 'integer',
            'slots_ids' => 'nullable|array',
            'slots_ids.*' => 'integer',
            'trigger_sessions' => 'nullable|array',
            'segment_id' => 'nullable|integer',
            'is_promo' => 'nullable|boolean',
            'is_external' => 'nullable|boolean',
            'author_id' => 'nullable|integer',
            'slots_id' => 'nullable|integer',
            'provider_id' => 'nullable|integer',
            'bonus_balance' => 'nullable|boolean',
            'count' => 'nullable|integer',
            'bet' => 'nullable|numeric',
            'bet_id' => 'nullable|string',
            'denomination' => 'nullable|numeric',
            'max_real_balance' => 'nullable|integer|min:1|max:999999',
            'genre_id' => [
                'nullable',
                'integer',
                Rule::in([0, 1, 2, 3, 4, 5, 6, 7, 8])
            ],
        ];
    }
}
