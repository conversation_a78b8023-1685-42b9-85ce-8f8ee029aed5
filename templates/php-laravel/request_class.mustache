{{>php_file_header}}

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
{{#imports}}use {{import}};
{{/imports}}

/**
 * {{classname}}Request
 * {{#description}}
 * {{.}}
 * {{/description}}
 */
class {{classname}}Request extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
{{#vars}}
            '{{name}}' => '{{#required}}required{{/required}}{{^required}}nullable{{/required}}{{#isString}}|string{{/isString}}{{#isNumeric}}|numeric{{/isNumeric}}{{#isBoolean}}|boolean{{/isBoolean}}{{#isArray}}|array{{/isArray}}',
{{/vars}}
        ];
    }
}
