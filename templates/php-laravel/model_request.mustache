{{>php_file_header}}

namespace {{packageName}}\Request;

{{#imports}}use {{import}};
{{/imports}}

/**
 * {{classname}}Request
 * {{#description}}
 * {{.}}
 * {{/description}}
 */
class {{classname}}Request
{
{{#vars}}
    /**
     * @var {{dataType}} ${{name}}{{#description}} {{.}}{{/description}}
     */
    public ${{name}};

{{/vars}}
    public function __construct(array $data = [])
    {
{{#vars}}
        $this->{{name}} = $data['{{name}}'] ?? null;
{{/vars}}
}

public function toArray(): array
{
return [
{{#vars}}
    '{{name}}' => $this->{{name}},
{{/vars}}
];
}
}
