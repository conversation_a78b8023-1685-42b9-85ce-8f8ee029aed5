<?php

namespace App\Services;

use App\Clients\AppClient;
use App\Exceptions\Exception;
use App\Models\PromoCode;
use App\Repositories\PromoCodeRepository;
use App\Services\Dto\BonusBalanceDTO;
use App\Services\Dto\PlayerPromoCodesDTO;
use App\Services\Dto\PromoCodeDTO;
use App\Services\Dto\PromoCodeSearchDTO;
use DateTimeImmutable;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\RecordsNotFoundException;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class PromoCodeService
{
    public function __construct(
        protected readonly PromoCodeRepository $promoCodeRepository,
        protected readonly BonusApplyService $bonusApplyService,
        protected readonly PlayerService $playerService,
        protected readonly SlotService $slotService,
        protected readonly SlotProviderService $slotProviderService,
        protected readonly AppClient $appClient,
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(PromoCodeDTO $dto): PromoCode
    {
        DB::beginTransaction();

        try {
            $promoCode = $this->fill($dto);
            $bonus = $promoCode->bonus();
            $bonus->update([
                'data' => ['promo_code' => $dto->code],
                'is_promo' => true,
            ]);
            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw new Exception($e->getMessage(), $e->getCode());
        }

        return $this->hydrateSinglePromoCodeWithAdditionalData($promoCode);
    }

    public function hydrateSinglePromoCodeWithAdditionalData(PromoCode $promoCode): PromoCode
    {
        return $this->hydratePromoCodesAdditionalData(collect([$promoCode]))->first();
    }

    /**
     * @param BaseCollection<int, PromoCode>|LengthAwarePaginator $promoCodes
     * @return BaseCollection<int, PromoCode>|LengthAwarePaginator
     */
    private function hydratePromoCodesAdditionalData(
        BaseCollection|LengthAwarePaginator $promoCodes
    ): BaseCollection|LengthAwarePaginator {

        $this->hydratePromoCodesWithSlots($promoCodes);
        $this->hydratePromoCodesWithSlotsProviders($promoCodes);

        return $promoCodes;
    }

    /**
     * @param BaseCollection<int, PromoCode>|LengthAwarePaginator $promoCodes
     * @return void
     */
    private function hydratePromoCodesWithSlots(BaseCollection|LengthAwarePaginator $promoCodes): void
    {
        $uniqueSlotsIds = $promoCodes->pluck('bonus.bonusSlots.*.slot_id')->flatten()->filter()
            ->unique()->toArray();

        $slots = $this->slotService->getSlotListByIds($uniqueSlotsIds);

        $promoCodes->map(static function (PromoCode $promoCode) use ($slots): void {
            $bosunSlots = $promoCode->bonus->bonusSlots->pluck('slot_id')->map(
                static fn(int $id) => $slots[$id] ?? null
            )->filter()->values();

            $promoCode->bonus->setAttribute('slots', $bosunSlots);
        });
    }

    /**
     * @param BaseCollection<int, PromoCode>|LengthAwarePaginator $promoCodes
     * @return void
     */
    private function hydratePromoCodesWithSlotsProviders(BaseCollection|LengthAwarePaginator $promoCodes): void
    {
        $slotProvidersIds = $promoCodes->pluck('bonus.bonusSlotProviders.*.slot_provider_id')->flatten()
            ->filter()->unique()->toArray();
        $bonusSlotProviders = $this->slotProviderService->getSlotProvidersByIds($slotProvidersIds);

        $promoCodes->map(static function (PromoCode $promoCode) use ($bonusSlotProviders): void {
            $bonusSlotProviders = $promoCode->bonus->bonusSlotProviders->pluck('slot_provider_id')->map(
                static fn(int $id) => $bonusSlotProviders[$id] ?? null
            )->filter()->values();

            $promoCode->bonus->setAttribute('slotProviders', $bonusSlotProviders);
        });
    }

    public function update(PromoCodeDTO $dto): PromoCode
    {
        $promoCode = $this->fill($dto);

        return $this->hydrateSinglePromoCodeWithAdditionalData($promoCode);
    }

    public function list(PromoCodeSearchDTO $dto): LengthAwarePaginator
    {
        $promoCodes = $this->promoCodeRepository->list($dto);

        $this->hydratePromoCodesAdditionalData($promoCodes);

        return $promoCodes;
    }

    /**
     * @param PlayerPromoCodesDTO $dto
     * @return Collection<int, PromoCode>
     */
    public function playerPromoCodes(PlayerPromoCodesDTO $dto): Collection
    {
        return $this->promoCodeRepository->playerPromoCodes($dto);
    }

    public function getPromoCode(string $code): ?PromoCode
    {
        $promoCode = $this->promoCodeRepository->getPromoCode($code);

        return $promoCode ? $this->hydrateSinglePromoCodeWithAdditionalData($promoCode) : null;
    }

    /**
     * @param int $playerId
     * @return int[]
     */
    public function getPromoCodeIdsByPlayerId(int $playerId): array
    {
        return $this->promoCodeRepository->getPromoCodeIdsByPlayerId($playerId);
    }

    public function fill(PromoCodeDTO $dto): PromoCode
    {
        $code = mb_strtoupper($dto->code);
        $promoCode = $dto->id ? $this->promoCodeRepository->getPromoCodeById($dto->id) : new PromoCode();
        $promoCode->client_id = config('app.special_client_id');
        $promoCode->name = $dto->name;
        $promoCode->code = $code;
        $promoCode->stream_id = $dto->streamId;
        $promoCode->bonus_id = $dto->bonusId;
        $promoCode->description = $dto->description ?? $code;
        $promoCode->use_limit = $dto->limit ?: null;
        $promoCode->start_at = $dto->startAt ?: new DateTimeImmutable();
        $promoCode->end_at = $dto->endAt ?: null;
        $promoCode->condition = $dto->condition;
        $promoCode->is_alanbase = $dto->isAlanbase ?? 0;

        if (null !== $dto->active) {
            $promoCode->is_active = $dto->active;
        }

        $this->promoCodeRepository->save($promoCode);

        return $promoCode;
    }

    /**
     * @throws Exception
     */
    public function apply(string $code, int $playerId): PromoCode
    {
        $promoCode = $this->promoCodeRepository->getPromoCode($code);

        if (!$promoCode) {
            throw new RecordsNotFoundException('Promo code not found', 404);
        }

        $player = $this->playerService->getPlayerById($playerId);

        DB::beginTransaction();

        try {
            $this->promoCodeRepository->attachPlayer($promoCode->id, $playerId);

            if ($promoCode->bonus->is_welcome_group) {
                $this->bonusApplyService->applyWelcomeBonusForPlayer($promoCode->bonus, $player);
            } elseif ($promoCode->bonus->is_onetime_group) {
                $this->bonusApplyService->applyOnetimeForPlayer($promoCode->bonus, $player);
            } elseif ($promoCode->bonus->is_no_dep) {
                $this->bonusApplyService->applyNoDepForPlayer($promoCode->bonus, $player);
            }

            ++$promoCode->uses;
            $this->promoCodeRepository->save($promoCode);

            $this->appClient->emitApplyPromoCodeEvents($playerId, $promoCode->code);

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw new Exception($e->getMessage(), $e->getCode());
        }

        return $this->hydrateSinglePromoCodeWithAdditionalData($promoCode);
    }

    public function attachBonusBalance(BonusBalanceDTO $dto): void
    {
        $this->promoCodeRepository->attachBonusBalance($dto);
    }

    public function getAlanbasePromoCodeByPlayerId(int $playerId): ?PromoCode
    {
        return $this->promoCodeRepository->getAlanbasePromoCodeByPlayerId($playerId);
    }
}
