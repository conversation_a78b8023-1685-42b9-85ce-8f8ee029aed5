<?php
/**
 * TemplateTest
 *
 * PHP version 8.1
 *
 * @category Class
 * @package  OpenAPI\Client
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Laravel
 *
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * Generated by: https://openapi-generator.tech
 * Generator version: 7.14.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the model.
 */

namespace OpenAPI\Client\Test\Model;

use PHPUnit\Framework\TestCase;

/**
 * TemplateTest Class Doc Comment
 *
 * @category    Class
 * @description Template
 * @package     OpenAPI\Client
 * <AUTHOR> Generator team
 * @link        https://openapi-generator.tech
 */
class TemplateTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "Template"
     */
    public function testTemplate()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "__version__"
     */
    public function testPropertyVersion()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "id"
     */
    public function testPropertyId()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "name"
     */
    public function testPropertyName()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "is_active"
     */
    public function testPropertyIsActive()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "max_bonus_number"
     */
    public function testPropertyMaxBonusNumber()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "type"
     */
    public function testPropertyType()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "operator_id"
     */
    public function testPropertyOperatorId()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "brand_id"
     */
    public function testPropertyBrandId()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "event_scheduled"
     */
    public function testPropertyEventScheduled()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "from_time"
     */
    public function testPropertyFromTime()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "to_time"
     */
    public function testPropertyToTime()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "days_to_use"
     */
    public function testPropertyDaysToUse()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "events_availability"
     */
    public function testPropertyEventsAvailability()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "restrictions"
     */
    public function testPropertyRestrictions()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "freebet_data"
     */
    public function testPropertyFreebetData()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }
}
