<?php
/**
 * BonusesApiTest
 * PHP version 8.1
 *
 * @category Class
 * @package  OpenAPI\Client
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Laravel
 *
 * Documentation for the Application API
 *
 * The version of the OpenAPI document: 1.0.0
 * Generated by: https://openapi-generator.tech
 * Generator version: 7.14.0-SNAPSHOT
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace OpenAPI\Client\Test\Api;

use \OpenAPI\Client\Configuration;
use \OpenAPI\Client\ApiException;
use \OpenAPI\Client\ObjectSerializer;
use PHPUnit\Framework\TestCase;

/**
 * BonusesApiTest Class Doc Comment
 *
 * @category Class
 * @package  OpenAPI\Client
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class BonusesApiTest extends TestCase
{

    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for createBonus
     *
     * .
     *
     */
    public function testCreateBonus()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test case for deleteBonus
     *
     * .
     *
     */
    public function testDeleteBonus()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }

    /**
     * Test case for updateBonus
     *
     * .
     *
     */
    public function testUpdateBonus()
    {
        // TODO: implement
        self::markTestIncomplete('Not implemented');
    }
}
