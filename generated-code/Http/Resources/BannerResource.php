<?php declare(strict_types=1);

/**
 * <PERSON>vel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BannerResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * BannerResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BannerResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(2)
    * @param null | string $language
    *
    * string(3)
    * @param null | string $country
    *
    * string(255)
    * @param string $isForSignedIn
    *
    * string(255)
    * @param string $platform
    *
    * boolean
    * @param bool $mobileApkInstall
    *
    * string(255)
    * @param string $location
    *
    * bigint
    * @param int $weight
    *
    * 
    * @param \OpenAPI\Server\Model\BannerResourceType $type
    *
    * 
    * @param object $disposition
    *
    * string(255)
    * @param string $image
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param null | int $smarticoSegmentId
    *
    * integer
    * @param null | int $startAt
    *
    * integer
    * @param null | int $endAt
    *
    * string(255)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $isForSignedIn,
        public string $platform,
        public bool $mobileApkInstall,
        public string $location,
        public int $weight = 0,
        public \OpenAPI\Server\Model\BannerResourceType $type,
        public object $disposition,
        public string $image,
        public bool $enabled,
        public bool $isActive,
        public ?string $language = null,
        public ?string $country = null,
        public ?int $smarticoSegmentId = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?string $uuid = null,
    ) {}
}

