<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * User
        */
        namespace OpenAPI\Server\Model;

        /**
        * User
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class User
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $clientId
    *
    * string(255)
    * @param string $login
    *
    * string(255)
    * @param string $email
    *
    * integer
    * @param null | int $roleId
    *
    * string(45)
    * @param null | string $lastSeenIp
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * datetime
    * @param null | \DateTime $deletedAt
    */

    public function __construct(
        public int $id,
        public int $clientId = 2,
        public string $login,
        public string $email,
        public ?int $roleId = null,
        public ?string $lastSeenIp = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?\DateTime $deletedAt = null,
    ) {}
}

