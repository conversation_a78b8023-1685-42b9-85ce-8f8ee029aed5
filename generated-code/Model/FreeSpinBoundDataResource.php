<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * FreeSpinBoundDataResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinBoundDataResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBoundDataResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $freeSpinId
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonusId
    *
    * bigint
    * @param int $betId
    *
    * bigint
    * @param int $bet
    *
    * integer
    * @param int $totalWin
    *
    * integer
    * @param int $countLeft
    *
    * string
    * @param string $message
    *
    * boolean
    * @param bool $isActive
    *
    * boolean
    * @param bool $isArchived
    *
    * boolean
    * @param bool $isUserNotified
    *
    * datetime
    * @param null | float $startAt
    *
    * datetime
    * @param null | float $expireAt
    *
    * datetime
    * @param null | float $canceledAt
    *
    * datetime
    * @param null | float $createdAt
    *
    * datetime
    * @param null | float $updatedAt
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $uuid,
        public int $betId,
        public int $bet,
        public int $totalWin,
        public int $countLeft,
        public string $message,
        public bool $isActive = true,
        public bool $isArchived = false,
        public bool $isUserNotified = false,
        public ?int $bonusId = null,
        public ?float $startAt = null,
        public ?float $expireAt = null,
        public ?float $canceledAt = null,
        public ?float $createdAt = null,
        public ?float $updatedAt = null,
    ) {}
}

