<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * Slot
        */
        namespace OpenAPI\Server\Model;

        /**
        * Slot
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Slot
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $provider
    *
    * integer
    * @param int $version
    *
    * bigint
    * @param int $externalProviderId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $slug
    *
    * string(255)
    * @param string $internalId
    *
    * string(255)
    * @param string $externalId
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $suspended
    *
    * text
    * @param string $meta
    *
    * string(255)
    * @param string $image
    *
    * text(16777215)
    * @param null | string $description
    *
    * boolean
    * @param bool $isMobile
    *
    * boolean
    * @param bool $isBonusReady
    *
    * boolean
    * @param bool $isWagerReady
    *
    * boolean
    * @param bool $isDesktop
    *
    * boolean
    * @param bool $hasLobby
    *
    * boolean
    * @param bool $hasFreespins
    *
    * boolean
    * @param null | bool $isDemo
    *
    * integer
    * @param int $genreId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * datetime
    * @param null | \DateTime $deletedAt
    *
    * string(255)
    * @param string $friendlyUrl
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $provider,
        public int $version,
        public int $externalProviderId,
        public string $name,
        public string $slug,
        public string $internalId,
        public string $externalId,
        public bool $enabled,
        public bool $suspended,
        public string $meta,
        public string $image,
        public bool $isMobile,
        public bool $isBonusReady = false,
        public bool $isWagerReady = false,
        public bool $isDesktop,
        public bool $hasLobby,
        public bool $hasFreespins = false,
        public int $genreId,
        public string $friendlyUrl,
        public ?string $description = null,
        public ?bool $isDemo = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?\DateTime $deletedAt = null,
        public ?string $uuid = null,
    ) {}
}

