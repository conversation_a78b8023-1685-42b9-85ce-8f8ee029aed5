<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * FreeSpinResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $generalProviderId
    *
    * bigint
    * @param null | int $aggregatorId
    *
    * bigint
    * @param int $slotId
    *
    * bigint
    * @param int $providerId
    *
    * bigint
    * @param int $bonusId
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $productName
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param string $betId
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * 
    * @param null | float $denomination
    *
    * boolean
    * @param bool $isActive
    *
    * bigint
    * @param int $authorId
    *
    * bigint
    * @param null | int $updatedByAuthorId
    *
    * boolean
    * @param bool $inProcess
    *
    * datetime
    * @param null | \DateTime $startAt
    *
    * datetime
    * @param null | \DateTime $expiredAt
    *
    * 
    * @param object $data
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param array[] $users
    *
    * 
    * @param \OpenAPI\Server\Model\User $author
    *
    * 
    * @param \OpenAPI\Server\Model\User $updateBy
    */

    public function __construct(
        public int $id,
        public int $generalProviderId,
        public int $slotId,
        public int $providerId,
        public int $bonusId,
        public string $type = 'manual',
        public string $name,
        public string $productName,
        public int $count,
        public int $bet,
        public string $betId = '0',
        public string $currency,
        public string $status = 'active',
        public bool $isActive = false,
        public int $authorId,
        public bool $inProcess = false,
        public object $data,
        public array $users,
        public \OpenAPI\Server\Model\User $author,
        public \OpenAPI\Server\Model\User $updateBy,
        public ?int $aggregatorId = null,
        public ?float $denomination = null,
        public ?int $updatedByAuthorId = null,
        public ?\DateTime $startAt = null,
        public ?\DateTime $expiredAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

