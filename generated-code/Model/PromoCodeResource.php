<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * PromoCodeResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * PromoCodeResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCodeResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $streamId
    *
    * bigint
    * @param int $bonusId
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $limit
    *
    * datetime
    * @param null | int $startAt
    *
    * datetime
    * @param null | int $endAt
    *
    * 
    * @param object $condition
    *
    * boolean
    * @param bool $isAlanbase
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $code,
        public int $bonusId,
        public bool $active = false,
        public int $uses,
        public int $limit,
        public object $condition,
        public bool $isAlanbase = false,
        public string $uuid,
        public \OpenAPI\Server\Model\BonusResource $bonus,
        public ?int $streamId = null,
        public ?string $description = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

