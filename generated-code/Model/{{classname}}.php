<?php declare(strict_types=1);

/**
 * <PERSON>vel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * ArrayOfUserObjectsInner
        */
        namespace OpenAPI\Client\Model;

        /**
        * ArrayOfUserObjectsInner
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class ArrayOfUserObjectsInner
{
    /**
    *
    * bigint
    * @param int $id
    */

    public function __construct(
        public int $id,
    ) {}
}

        /**
        * BannerResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BannerResource
        */
            use <PERSON><PERSON>\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BannerResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(2)
    * @param null | string $language
    *
    * string(3)
    * @param null | string $country
    *
    * string(255)
    * @param string $is_for_signed_in
    *
    * string(255)
    * @param string $platform
    *
    * boolean
    * @param bool $mobile_apk_install
    *
    * string(255)
    * @param string $location
    *
    * bigint
    * @param int $weight
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param object $disposition
    *
    * string(255)
    * @param string $image
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $is_active
    *
    * integer
    * @param null | int $smartico_segment_id
    *
    * integer
    * @param null | int $start_at
    *
    * integer
    * @param null | int $end_at
    *
    * string(255)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $is_for_signed_in,
        public string $platform,
        public bool $mobile_apk_install,
        public string $location,
        public int $weight = 0,
        public string $type,
        public object $disposition,
        public string $image,
        public bool $enabled,
        public bool $is_active,
        public ?string $language = null,
        public ?string $country = null,
        public ?int $smartico_segment_id = null,
        public ?int $start_at = null,
        public ?int $end_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * Bonus
        */
        namespace OpenAPI\Client\Model;

        /**
        * Bonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Bonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * json
    * @param null | string $bonus_name
    *
    * text
    * @param null | string $description
    *
    * json
    * @param null | string $condition
    *
    * string(40)
    * @param string $type
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param string $bonuses
    *
    * json
    * @param string $max_transfers
    *
    * json
    * @param null | string $deposit_factors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $min_deposit
    *
    * float
    * @param null | float $min_factor
    *
    * bigint
    * @param null | int $min_bet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $total_transferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $total_uses
    *
    * boolean
    * @param bool $is_external
    *
    * boolean
    * @param bool $is_promo
    *
    * datetime
    * @param null | \DateTime $active_from
    *
    * datetime
    * @param null | \DateTime $active_til
    *
    * bigint
    * @param null | int $duration
    *
    * text
    * @param string $data
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param int[] $slot_providers
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $active_trigger_sessions
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $type = 'wager',
        public string $bonuses,
        public string $max_transfers,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $total_transferred,
        public int $uses,
        public int $transfers,
        public int $total_uses,
        public bool $is_external = false,
        public bool $is_promo = false,
        public string $data,
        public array $slot_providers,
        public \OpenAPI\Client\Model\FreeSpin $freespin,
        public array $trigger_sessions,
        public array $active_trigger_sessions,
        public ?string $bonus_name = null,
        public ?string $description = null,
        public ?string $condition = null,
        public ?string $image = null,
        public ?string $deposit_factors = null,
        public ?int $min_deposit = null,
        public ?float $min_factor = null,
        public ?int $min_bet = null,
        public ?\DateTime $active_from = null,
        public ?\DateTime $active_til = null,
        public ?int $duration = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * BonusBalance
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusBalance
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param null | string $log_key
    *
    * bigint
    * @param int $bonus_id
    *
    * string(40)
    * @param string $type
    *
    * bigint
    * @param int $player_id
    *
    * bigint
    * @param int $balance
    *
    * string(255)
    * @param null | string $bonus_external_id
    *
    * string(40)
    * @param string $status
    *
    * bigint
    * @param int $orig_bonus
    *
    * bigint
    * @param int $orig_wager
    *
    * bigint
    * @param int $wager
    *
    * float
    * @param null | float $min_factor
    *
    * bigint
    * @param null | int $min_bet
    *
    * datetime
    * @param null | \DateTime $expire_at
    *
    * bigint
    * @param int $in_game
    *
    * bigint
    * @param int $transfer
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $popup_seen
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\User $player
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public string $type = 'wager',
        public int $player_id,
        public int $balance,
        public string $status,
        public int $orig_bonus,
        public int $orig_wager,
        public int $wager,
        public int $in_game,
        public int $transfer,
        public string $currency,
        public bool $active,
        public bool $popup_seen = false,
        public bool $casino,
        public bool $bets,
        public \OpenAPI\Client\Model\User $player,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?string $log_key = null,
        public ?string $bonus_external_id = null,
        public ?float $min_factor = null,
        public ?int $min_bet = null,
        public ?\DateTime $expire_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * BonusForPlayerResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusForPlayerResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResource
{
    /**
    *
    * 
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * boolean
    * @param bool $crash
    *
    * 
    * @param \OpenAPI\Client\Model\BonusForPlayerResourceMaxBonus $max_bonus
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $deposit_factors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $min_deposit
    *
    * bigint
    * @param null | int $min_bet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonus_data
    *
    * boolean
    * @param bool $is_welcome
    *
    * boolean
    * @param bool $is_onetime
    *
    * boolean
    * @param bool $is_no_dep
    *
    * boolean
    * @param bool $is_deposit
    *
    * boolean
    * @param bool $is_free_spin_for_deposit
    *
    * 
    * @param int $weight
    *
    * boolean
    * @param bool $is_organic
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    */

    public function __construct(
        public int $id,
        public string $name,
        public object $bonus_name,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public bool $crash = false,
        public \OpenAPI\Client\Model\BonusForPlayerResourceMaxBonus $max_bonus,
        public array $max_bonuses,
        public array $max_transfers,
        public array $deposit_factors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public object $bonus_data,
        public bool $is_welcome,
        public bool $is_onetime,
        public bool $is_no_dep,
        public bool $is_deposit,
        public bool $is_free_spin_for_deposit,
        public int $weight,
        public bool $is_organic,
        public array $trigger_sessions,
        public ?string $image = null,
        public ?int $min_deposit = null,
        public ?int $min_bet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
    ) {}
}

        /**
        * BonusForPlayerResourceMaxBonus
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusForPlayerResourceMaxBonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResourceMaxBonus
{
    /**
    *
    * 
    * @param string $amount
    *
    * string(3)
    * @param string $currency
    */

    public function __construct(
        public string $amount,
        public string $currency,
    ) {}
}

        /**
        * BonusHistoryLogResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusHistoryLogResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusHistoryLogResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $bonus_id
    *
    * 
    * @param string $type
    *
    * 
    * @param string $author_email
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $created_at
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public string $type,
        public string $author_email,
        public array $data,
        public \DateTime $created_at,
    ) {}
}

        /**
        * BonusInfo
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | \DateTime $visible_from
    *
    * datetime
    * @param null | \DateTime $visible_to
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $is_welcome
    *
    * boolean
    * @param bool $is_for_smartico
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param null | string $bonus_name
    *
    * json
    * @param null | string $description
    *
    * json
    * @param null | string $description_info
    *
    * json
    * @param null | string $condition_title
    *
    * json
    * @param null | string $condition
    *
    * json
    * @param null | string $colors
    *
    * integer
    * @param int $sort_order
    *
    * string(255)
    * @param null | string $proceed_link
    *
    * bigint
    * @param null | int $bonus_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $is_for_smartico = false,
        public int $sort_order,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?\DateTime $visible_from = null,
        public ?\DateTime $visible_to = null,
        public ?bool $is_welcome = false,
        public ?string $image = null,
        public ?string $bonus_name = null,
        public ?string $description = null,
        public ?string $description_info = null,
        public ?string $condition_title = null,
        public ?string $condition = null,
        public ?string $colors = null,
        public ?string $proceed_link = null,
        public ?int $bonus_id = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * BonusInfoResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusInfoResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | int $visible_from
    *
    * datetime
    * @param null | int $visible_to
    *
    * datetime
    * @param null | int $time_from
    *
    * datetime
    * @param null | int $timer_to
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $is_welcome
    *
    * boolean
    * @param bool $is_for_smartico
    *
    * string(255)
    * @param null | string $image
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param object $description
    *
    * 
    * @param object $description_info
    *
    * 
    * @param object $condition_title
    *
    * 
    * @param object $condition
    *
    * 
    * @param null | string[] $colors
    *
    * 
    * @param \OpenAPI\Client\Model\BonusInfoResourceBonus $bonus
    *
    * boolean
    * @param bool $show_visible_date
    *
    * boolean
    * @param null | bool $custom_type
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $is_for_smartico = false,
        public object $bonus_name,
        public object $description,
        public object $description_info,
        public object $condition_title,
        public object $condition,
        public \OpenAPI\Client\Model\BonusInfoResourceBonus $bonus,
        public bool $show_visible_date = false,
        public ?int $visible_from = null,
        public ?int $visible_to = null,
        public ?int $time_from = null,
        public ?int $timer_to = null,
        public ?bool $is_welcome = false,
        public ?string $image = null,
        public ?array $colors = null,
        public ?bool $custom_type = null,
    ) {}
}

        /**
        * BonusInfoResourceBonus
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusInfoResourceBonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResourceBonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param int[] $deposit_factors
    *
    * 
    * @param null | int $max_bonuses
    */

    public function __construct(
        public int $id,
        public string $type = 'wager',
        public array $deposit_factors,
        public ?int $max_bonuses = null,
    ) {}
}

        /**
        * BonusPlayer
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusPlayer
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayer
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonus_id
    *
    * bigint
    * @param int $player_id
    *
    * bigint
    * @param null | int $trigger_session_id
    *
    * string(255)
    * @param string $status
    *
    * integer
    * @param null | int $deposits_count
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public int $player_id,
        public string $status,
        public ?int $trigger_session_id = null,
        public ?int $deposits_count = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * BonusPlayerCard
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusPlayerCard
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayerCard
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $player_id
    *
    * boolean
    * @param bool $is_used
    *
    * integer
    * @param null | int $bonus_id
    *
    * integer
    * @param null | int $bonus_info_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\User $player
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $player_id,
        public bool $is_used = false,
        public \OpenAPI\Client\Model\User $player,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?int $bonus_id = null,
        public ?int $bonus_info_id = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * BonusResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $weight
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $deposit_factors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $min_deposit
    *
    * float
    * @param null | float $min_factor
    *
    * bigint
    * @param null | int $min_bet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $total_transferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $total_uses
    *
    * boolean
    * @param bool $is_external
    *
    * boolean
    * @param bool $is_promo
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonus_data
    *
    * boolean
    * @param bool $is_welcome
    *
    * boolean
    * @param bool $is_onetime
    *
    * boolean
    * @param bool $is_no_dep
    *
    * boolean
    * @param bool $is_deposit
    *
    * boolean
    * @param bool $is_free_spin_for_deposit
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param int[] $slot_providers
    *
    * 
    * @param int[] $slots
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param null | int $max_real_balance
    *
    * 
    * @param null | int $genre_id
    */

    public function __construct(
        public int $id,
        public int $weight,
        public string $name,
        public object $bonus_name,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public array $max_bonuses,
        public array $max_transfers,
        public array $deposit_factors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $total_transferred,
        public int $uses,
        public int $transfers,
        public int $total_uses,
        public bool $is_external = false,
        public bool $is_promo = false,
        public object $bonus_data,
        public bool $is_welcome,
        public bool $is_onetime,
        public bool $is_no_dep,
        public bool $is_deposit,
        public bool $is_free_spin_for_deposit,
        public array $slot_providers,
        public array $slots,
        public \OpenAPI\Client\Model\FreeSpin $freespin,
        public array $trigger_sessions,
        public ?string $image = null,
        public ?int $min_deposit = null,
        public ?float $min_factor = null,
        public ?int $min_bet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?int $max_real_balance = null,
        public ?int $genre_id = null,
    ) {}
}

        /**
        * BonusTriggerSession
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusTriggerSession
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusTriggerSession
{
    /**
    *
    * datetime
    * @param \DateTime $start_at
    *
    * datetime
    * @param \DateTime $end_at
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public \DateTime $start_at,
        public \DateTime $end_at,
        public ?string $uuid = null,
    ) {}
}

        /**
        * CreateBonusRequest
        */
        namespace OpenAPI\Client\Model;

        /**
        * CreateBonusRequest
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class CreateBonusRequest
{
    /**
    *
    * 1 - 999
    * @param float $weight
    *
    * 
    * @param string $name
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param string $type
    *
    * 
    * @param null | int $min_bet
    *
    * 
    * @param null | int $min_deposit
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $deposit_factors
    *
    * 
    * @param null | int $duration
    *
    * 
    * @param null | float $wager
    *
    * 
    * @param null | string $currency
    *
    * 
    * @param null | bool $active
    *
    * 
    * @param null | bool $casino
    *
    * 
    * @param null | bool $bets
    *
    * 
    * @param null | string $from
    *
    * 
    * @param null | string $to
    *
    * 
    * @param null | float $min_factor
    *
    * 
    * @param object $data
    *
    * 
    * @param int[] $providers_ids
    *
    * 
    * @param int[] $slots_ids
    *
    * required if type is free_spins_for_deposit
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param null | int $segment_id
    *
    * 
    * @param null | bool $is_promo
    *
    * 
    * @param null | bool $is_external
    *
    * required if type is free_spins_for_deposit
    * @param null | int $author_id
    *
    * required if type is free_spins_for_deposit
    * @param null | int $slots_id
    *
    * required if type is free_spins_for_deposit
    * @param null | int $provider_id
    *
    * required if type is free_spins_for_deposit
    * @param null | bool $bonus_balance
    *
    * required if type is free_spins_for_deposit
    * @param null | int $count
    *
    * required if type is free_spins_for_deposit
    * @param null | float $bet
    *
    * required if type is free_spins_for_deposit
    * @param null | string $bet_id
    *
    * required if type is free_spins_for_deposit
    * @param null | float $denomination
    *
    * 
    * @param null | int $max_real_balance
    *
    * 
    * @param null | int $genre_id
    */

    public function __construct(
        public float $weight,
        public string $name,
        public string $image,
        public object $description,
        public object $condition,
        public object $bonus_name,
        public string $type,
        public array $max_transfers,
        public array $max_bonuses,
        public array $deposit_factors,
        public object $data,
        public array $providers_ids,
        public array $slots_ids,
        public array $trigger_sessions,
        public ?int $min_bet = null,
        public ?int $min_deposit = null,
        public ?int $duration = null,
        public ?float $wager = null,
        public ?string $currency = null,
        public ?bool $active = null,
        public ?bool $casino = null,
        public ?bool $bets = null,
        public ?string $from = null,
        public ?string $to = null,
        public ?float $min_factor = null,
        public ?int $segment_id = null,
        public ?bool $is_promo = null,
        public ?bool $is_external = null,
        public ?int $author_id = null,
        public ?int $slots_id = null,
        public ?int $provider_id = null,
        public ?bool $bonus_balance = null,
        public ?int $count = null,
        public ?float $bet = null,
        public ?string $bet_id = null,
        public ?float $denomination = null,
        public ?int $max_real_balance = null,
        public ?int $genre_id = null,
    ) {}
}

        /**
        * DataSubscriptions
        */
        namespace OpenAPI\Client\Model;

        /**
        * DataSubscriptions
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DataSubscriptions
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $sub_id
    *
    * string(255)
    * @param string $access_token
    *
    * string(255)
    * @param null | string $game_token
    *
    * string(255)
    * @param string $provider
    *
    * string(255)
    * @param string $provider_company
    *
    * boolean
    * @param bool $is_approved
    *
    * boolean
    * @param bool $is_slot
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $sub_id,
        public string $access_token,
        public string $provider,
        public string $provider_company,
        public bool $is_approved = false,
        public bool $is_slot = false,
        public ?string $game_token = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * DepositBonusInfo
        */
        namespace OpenAPI\Client\Model;

        /**
        * DepositBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DepositBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonus_id
    *
    * bigint
    * @param int $smartico_bonus_id
    *
    * bigint
    * @param null | int $player_id
    *
    * string(255)
    * @param null | string $email
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public int $smartico_bonus_id,
        public string $status = 'in_process',
        public ?int $player_id = null,
        public ?string $email = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * FreeSpin
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpin
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpin
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $slot_id
    *
    * bigint
    * @param int $provider_id
    *
    * string(255)
    * @param null | string $bonus_id
    *
    * string(255)
    * @param string $type
    *
    * string(50)
    * @param string $name
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param null | string $bet_id
    *
    * string(255)
    * @param null | string $denomination
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * boolean
    * @param bool $is_active
    *
    * bigint
    * @param int $author_id
    *
    * bigint
    * @param null | int $updated_by_author_id
    *
    * boolean
    * @param bool $in_process
    *
    * datetime
    * @param null | \DateTime $start_at
    *
    * datetime
    * @param null | \DateTime $expired_at
    *
    * json
    * @param null | string $data
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpinBound[] $bounds
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    *
    * 
    * @param \OpenAPI\Client\Model\User $author
    *
    * 
    * @param \OpenAPI\Client\Model\User $update_by
    */

    public function __construct(
        public int $id,
        public int $slot_id,
        public int $provider_id,
        public string $type = 'manual',
        public string $name,
        public int $count,
        public int $bet,
        public string $currency,
        public string $status = 'active',
        public bool $is_active = false,
        public int $author_id,
        public bool $in_process = false,
        public array $bounds,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public \OpenAPI\Client\Model\User $author,
        public \OpenAPI\Client\Model\User $update_by,
        public ?string $bonus_id = null,
        public ?string $bet_id = null,
        public ?string $denomination = null,
        public ?int $updated_by_author_id = null,
        public ?\DateTime $start_at = null,
        public ?\DateTime $expired_at = null,
        public ?string $data = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * FreeSpinBound
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinBound
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBound
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $free_spin_id
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonus_id
    *
    * bigint
    * @param int $bet
    *
    * string(255)
    * @param string $bet_id
    *
    * integer
    * @param int $total_win
    *
    * integer
    * @param int $count_left
    *
    * boolean
    * @param bool $is_active
    *
    * boolean
    * @param bool $is_archived
    *
    * text(65535)
    * @param null | string $message
    *
    * datetime
    * @param null | float $start_at
    *
    * datetime
    * @param null | float $expire_at
    *
    * datetime
    * @param null | float $canceled_at
    *
    * boolean
    * @param bool $is_user_notified
    *
    * datetime
    * @param null | float $created_at
    *
    * datetime
    * @param null | float $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\Player $player_info
    */

    public function __construct(
        public int $id,
        public int $free_spin_id,
        public string $uuid,
        public int $bet,
        public string $bet_id,
        public int $total_win,
        public int $count_left,
        public bool $is_active = true,
        public bool $is_archived = false,
        public bool $is_user_notified = false,
        public \OpenAPI\Client\Model\Player $player_info,
        public ?int $bonus_id = null,
        public ?string $message = null,
        public ?float $start_at = null,
        public ?float $expire_at = null,
        public ?float $canceled_at = null,
        public ?float $created_at = null,
        public ?float $updated_at = null,
    ) {}
}

        /**
        * FreeSpinBoundDataResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinBoundDataResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBoundDataResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $free_spin_id
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonus_id
    *
    * bigint
    * @param int $bet_id
    *
    * bigint
    * @param int $bet
    *
    * integer
    * @param int $total_win
    *
    * integer
    * @param int $count_left
    *
    * string
    * @param string $message
    *
    * boolean
    * @param bool $is_active
    *
    * boolean
    * @param bool $is_archived
    *
    * boolean
    * @param bool $is_user_notified
    *
    * datetime
    * @param null | float $start_at
    *
    * datetime
    * @param null | float $expire_at
    *
    * datetime
    * @param null | float $canceled_at
    *
    * datetime
    * @param null | float $created_at
    *
    * datetime
    * @param null | float $updated_at
    */

    public function __construct(
        public int $id,
        public int $free_spin_id,
        public string $uuid,
        public int $bet_id,
        public int $bet,
        public int $total_win,
        public int $count_left,
        public string $message,
        public bool $is_active = true,
        public bool $is_archived = false,
        public bool $is_user_notified = false,
        public ?int $bonus_id = null,
        public ?float $start_at = null,
        public ?float $expire_at = null,
        public ?float $canceled_at = null,
        public ?float $created_at = null,
        public ?float $updated_at = null,
    ) {}
}

        /**
        * FreeSpinBoundResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinBoundResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBoundResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $free_spin_id
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonus_id
    *
    * bigint
    * @param int $bet
    *
    * integer
    * @param int $total_win
    *
    * integer
    * @param int $count_left
    *
    * boolean
    * @param bool $is_active
    *
    * boolean
    * @param bool $is_archived
    *
    * datetime
    * @param null | float $start_at
    *
    * datetime
    * @param null | float $expire_at
    *
    * datetime
    * @param null | float $canceled_at
    *
    * datetime
    * @param null | float $created_at
    *
    * datetime
    * @param null | float $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpinResource $freespin
    *
    * 
    * @param int $slot_id
    *
    * 
    * @param \OpenAPI\Client\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $free_spin_id,
        public string $uuid,
        public int $bet,
        public int $total_win,
        public int $count_left,
        public bool $is_active = true,
        public bool $is_archived = false,
        public \OpenAPI\Client\Model\FreeSpinResource $freespin,
        public int $slot_id,
        public \OpenAPI\Client\Model\BonusResource $bonus,
        public ?int $bonus_id = null,
        public ?float $start_at = null,
        public ?float $expire_at = null,
        public ?float $canceled_at = null,
        public ?float $created_at = null,
        public ?float $updated_at = null,
    ) {}
}

        /**
        * FreeSpinHistoryLogResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinHistoryLogResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinHistoryLogResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $free_spin_id
    *
    * 
    * @param string $type
    *
    * 
    * @param string $author_email
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $created_at
    */

    public function __construct(
        public int $id,
        public int $free_spin_id,
        public string $type,
        public string $author_email,
        public array $data,
        public \DateTime $created_at,
    ) {}
}

        /**
        * FreeSpinResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $general_provider_id
    *
    * bigint
    * @param null | int $aggregator_id
    *
    * bigint
    * @param int $slot_id
    *
    * bigint
    * @param int $provider_id
    *
    * bigint
    * @param int $bonus_id
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $product_name
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param string $bet_id
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * 
    * @param null | float $denomination
    *
    * boolean
    * @param bool $is_active
    *
    * bigint
    * @param int $author_id
    *
    * bigint
    * @param null | int $updated_by_author_id
    *
    * boolean
    * @param bool $in_process
    *
    * datetime
    * @param null | \DateTime $start_at
    *
    * datetime
    * @param null | \DateTime $expired_at
    *
    * 
    * @param object $data
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param array[] $users
    *
    * 
    * @param \OpenAPI\Client\Model\User $author
    *
    * 
    * @param \OpenAPI\Client\Model\User $update_by
    */

    public function __construct(
        public int $id,
        public int $general_provider_id,
        public int $slot_id,
        public int $provider_id,
        public int $bonus_id,
        public string $type = 'manual',
        public string $name,
        public string $product_name,
        public int $count,
        public int $bet,
        public string $bet_id = '0',
        public string $currency,
        public string $status = 'active',
        public bool $is_active = false,
        public int $author_id,
        public bool $in_process = false,
        public object $data,
        public array $users,
        public \OpenAPI\Client\Model\User $author,
        public \OpenAPI\Client\Model\User $update_by,
        public ?int $aggregator_id = null,
        public ?float $denomination = null,
        public ?int $updated_by_author_id = null,
        public ?\DateTime $start_at = null,
        public ?\DateTime $expired_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * MassFreeBetBonusInfo
        */
        namespace OpenAPI\Client\Model;

        /**
        * MassFreeBetBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class MassFreeBetBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonus_id
    *
    * string(255)
    * @param null | string $smartico_bonus_id
    *
    * bigint
    * @param null | int $template_id
    *
    * string(255)
    * @param null | string $external_player_id
    *
    * string(255)
    * @param string $currency
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * bigint
    * @param null | int $player_id
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public string $currency,
        public string $status = 'in_process',
        public ?string $smartico_bonus_id = null,
        public ?int $template_id = null,
        public ?string $external_player_id = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?int $player_id = null,
    ) {}
}

        /**
        * MassOnetimeBonusInfo
        */
        namespace OpenAPI\Client\Model;

        /**
        * MassOnetimeBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class MassOnetimeBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonus_id
    *
    * string(255)
    * @param null | string $smartico_bonus_id
    *
    * bigint
    * @param null | int $player_id
    *
    * string(255)
    * @param null | string $email
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public string $status = 'in_process',
        public ?string $smartico_bonus_id = null,
        public ?int $player_id = null,
        public ?string $email = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * PaginationFirstAndLastLinks
        */
        namespace OpenAPI\Client\Model;

        /**
        * PaginationFirstAndLastLinks
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationFirstAndLastLinks
{
    /**
    *
    * First
    * @param string $first
    *
    * Last
    * @param string $last
    *
    * Previous
    * @param null | string $prev
    *
    * Next
    * @param string $next
    */

    public function __construct(
        public string $first,
        public string $last,
        public string $next,
        public ?string $prev = null,
    ) {}
}

        /**
        * PaginationLinksInner
        */
        namespace OpenAPI\Client\Model;

        /**
        * PaginationLinksInner
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationLinksInner
{
    /**
    *
    * URL
    * @param string $url
    *
    * Label
    * @param float $label
    *
    * Active
    * @param bool $active
    */

    public function __construct(
        public string $url,
        public float $label,
        public bool $active,
    ) {}
}

        /**
        * PaginationMeta
        */
        namespace OpenAPI\Client\Model;

        /**
        * PaginationMeta
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationMeta
{
    /**
    *
    * Current page
    * @param int $current_page
    *
    * From
    * @param int $from
    *
    * Last page
    * @param int $last_page
    *
    * 
    * @param \OpenAPI\Client\Model\PaginationLinksInner[] $links
    *
    * Path
    * @param string $path
    *
    * Items per page
    * @param int $per_page
    *
    * Total pages
    * @param int $to
    *
    * Total items
    * @param int $total
    */

    public function __construct(
        public int $current_page = 1,
        public int $from = 1,
        public int $last_page = 11,
        public array $links,
        public string $path = 'http://localhost:8000/api/bonuses',
        public int $per_page = 15,
        public int $to,
        public int $total,
    ) {}
}

        /**
        * Player
        */
        namespace OpenAPI\Client\Model;

        /**
        * Player
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Player
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param null | string $username
    *
    * string(255)
    * @param string $password
    *
    * string(255)
    * @param null | string $email
    *
    * boolean
    * @param bool $is_under_moderation
    *
    * boolean
    * @param bool $payout_without_moderation
    *
    * boolean
    * @param bool $subscribed
    *
    * integer
    * @param null | int $bonus_balance_id
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $email_verified_at
    *
    * string(255)
    * @param null | string $phone
    *
    * string(6)
    * @param null | string $phone_code
    *
    * datetime
    * @param null | \DateTime $phone_verified_at
    *
    * string(255)
    * @param string $first_name
    *
    * string(255)
    * @param null | string $last_name
    *
    * date
    * @param null | \DateTime $birth
    *
    * string(2)
    * @param null | string $country
    *
    * string(255)
    * @param null | string $city
    *
    * string(7)
    * @param null | string $gender
    *
    * string(2)
    * @param null | string $language
    *
    * string(255)
    * @param null | string $click_id
    *
    * string(3)
    * @param string $currency
    *
    * string(45)
    * @param null | string $last_seen_ip
    *
    * boolean
    * @param bool $blocked
    *
    * text(65535)
    * @param null | string $comment
    *
    * integer
    * @param null | int $welcome_bonus_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $password,
        public bool $is_under_moderation = false,
        public bool $payout_without_moderation = false,
        public bool $subscribed = false,
        public string $uuid,
        public string $first_name,
        public string $currency,
        public bool $blocked = false,
        public ?string $username = null,
        public ?string $email = null,
        public ?int $bonus_balance_id = null,
        public ?\DateTime $email_verified_at = null,
        public ?string $phone = null,
        public ?string $phone_code = null,
        public ?\DateTime $phone_verified_at = null,
        public ?string $last_name = null,
        public ?\DateTime $birth = null,
        public ?string $country = null,
        public ?string $city = null,
        public ?string $gender = null,
        public ?string $language = null,
        public ?string $click_id = null,
        public ?string $last_seen_ip = null,
        public ?string $comment = null,
        public ?int $welcome_bonus_id = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * PromoCode
        */
        namespace OpenAPI\Client\Model;

        /**
        * PromoCode
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCode
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $stream_id
    *
    * bigint
    * @param int $bonus_id
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $is_active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $use_limit
    *
    * datetime
    * @param null | int $start_at
    *
    * datetime
    * @param null | int $end_at
    *
    * string
    * @param object $condition
    *
    * boolean
    * @param bool $is_alanbase
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $code,
        public int $bonus_id,
        public bool $is_active = false,
        public int $uses,
        public int $use_limit,
        public object $condition,
        public bool $is_alanbase = false,
        public string $uuid,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?int $stream_id = null,
        public ?string $description = null,
        public ?int $start_at = null,
        public ?int $end_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * PromoCodeResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * PromoCodeResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCodeResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $stream_id
    *
    * bigint
    * @param int $bonus_id
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $limit
    *
    * datetime
    * @param null | int $start_at
    *
    * datetime
    * @param null | int $end_at
    *
    * 
    * @param object $condition
    *
    * boolean
    * @param bool $is_alanbase
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $code,
        public int $bonus_id,
        public bool $active = false,
        public int $uses,
        public int $limit,
        public object $condition,
        public bool $is_alanbase = false,
        public string $uuid,
        public \OpenAPI\Client\Model\BonusResource $bonus,
        public ?int $stream_id = null,
        public ?string $description = null,
        public ?int $start_at = null,
        public ?int $end_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

        /**
        * Slot
        */
        namespace OpenAPI\Client\Model;

        /**
        * Slot
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Slot
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $provider
    *
    * integer
    * @param int $version
    *
    * bigint
    * @param int $external_provider_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $slug
    *
    * string(255)
    * @param string $internal_id
    *
    * string(255)
    * @param string $external_id
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $suspended
    *
    * text
    * @param string $meta
    *
    * string(255)
    * @param string $image
    *
    * text(16777215)
    * @param null | string $description
    *
    * boolean
    * @param bool $is_mobile
    *
    * boolean
    * @param bool $is_bonus_ready
    *
    * boolean
    * @param bool $is_wager_ready
    *
    * boolean
    * @param bool $is_desktop
    *
    * boolean
    * @param bool $has_lobby
    *
    * boolean
    * @param bool $has_freespins
    *
    * boolean
    * @param null | bool $is_demo
    *
    * integer
    * @param int $genre_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * datetime
    * @param null | \DateTime $deleted_at
    *
    * string(255)
    * @param string $friendly_url
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $provider,
        public int $version,
        public int $external_provider_id,
        public string $name,
        public string $slug,
        public string $internal_id,
        public string $external_id,
        public bool $enabled,
        public bool $suspended,
        public string $meta,
        public string $image,
        public bool $is_mobile,
        public bool $is_bonus_ready = false,
        public bool $is_wager_ready = false,
        public bool $is_desktop,
        public bool $has_lobby,
        public bool $has_freespins = false,
        public int $genre_id,
        public string $friendly_url,
        public ?string $description = null,
        public ?bool $is_demo = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?\DateTime $deleted_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * SlotsProvider
        */
        namespace OpenAPI\Client\Model;

        /**
        * SlotsProvider
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class SlotsProvider
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * bigint
    * @param int $subscription_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param null | string $custom_name
    *
    * string(255)
    * @param null | string $image
    *
    * boolean
    * @param bool $suspended
    *
    * boolean
    * @param bool $is_bonus_barred
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\Slot[] $get_providers
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public int $subscription_id,
        public string $name,
        public bool $suspended = false,
        public bool $is_bonus_barred = false,
        public array $get_providers,
        public ?string $custom_name = null,
        public ?string $image = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * Template
        */
        namespace OpenAPI\Client\Model;

        /**
        * Template
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Template
{
    /**
    *
    * bigint
    * @param int $__version__
    *
    * string(255)
    * @param string $id
    *
    * text
    * @param null | string $name
    *
    * boolean
    * @param bool $is_active
    *
    * integer
    * @param int $max_bonus_number
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $operator_id
    *
    * string(255)
    * @param string $brand_id
    *
    * datetime
    * @param null | string $event_scheduled
    *
    * datetime
    * @param null | float $from_time
    *
    * datetime
    * @param null | float $to_time
    *
    * 
    * @param float $days_to_use
    *
    * text
    * @param null | string $events_availability
    *
    * text
    * @param null | string $restrictions
    *
    * 
    * @param \OpenAPI\Client\Model\TemplateFreebetData $freebet_data
    */

    public function __construct(
        public int $__version__,
        public string $id,
        public bool $is_active = false,
        public int $max_bonus_number,
        public string $type,
        public string $operator_id,
        public string $brand_id,
        public float $days_to_use,
        public \OpenAPI\Client\Model\TemplateFreebetData $freebet_data,
        public ?string $name = null,
        public ?string $event_scheduled = null,
        public ?float $from_time = null,
        public ?float $to_time = null,
        public ?string $events_availability = null,
        public ?string $restrictions = null,
    ) {}
}

        /**
        * TemplateFreebetData
        */
        namespace OpenAPI\Client\Model;

        /**
        * TemplateFreebetData
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetData
{
    /**
    *
    * bigint
    * @param int $__version__
    *
    * boolean
    * @param bool $is_api_amount
    *
    * 
    * @param int[] $amount_list
    *
    * string(255)
    * @param string $type
    *
    * integer
    * @param int $min_selection
    *
    * integer
    * @param int $max_selection
    *
    * 
    * @param int $min_odd
    *
    * 
    * @param int $max_odd
    *
    * 
    * @param bool $bound_refund
    *
    * 
    * @param object $description
    *
    * string(255)
    * @param null | string $reserve_template
    *
    * 
    * @param \OpenAPI\Client\Model\TemplateFreebetDataBetRestrictions $bet_restrictions
    */

    public function __construct(
        public int $__version__,
        public bool $is_api_amount = false,
        public array $amount_list,
        public string $type,
        public int $min_selection,
        public int $max_selection,
        public int $min_odd,
        public int $max_odd,
        public bool $bound_refund,
        public object $description,
        public \OpenAPI\Client\Model\TemplateFreebetDataBetRestrictions $bet_restrictions,
        public ?string $reserve_template = null,
    ) {}
}

        /**
        * TemplateFreebetDataBetRestrictions
        */
        namespace OpenAPI\Client\Model;

        /**
        * TemplateFreebetDataBetRestrictions
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetDataBetRestrictions
{
    /**
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param string[] $bets_data
    */

    public function __construct(
        public string $type,
        public array $bets_data,
    ) {}
}

        /**
        * UpdateBonusRequest
        */
        namespace OpenAPI\Client\Model;

        /**
        * UpdateBonusRequest
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UpdateBonusRequest
{
    /**
    *
    * 
    * @param string $name
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param string $type
    *
    * 
    * @param null | int $min_bet
    *
    * 
    * @param null | int $min_deposit
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $deposit_factors
    *
    * 
    * @param null | int $duration
    *
    * 
    * @param null | float $wager
    *
    * 
    * @param null | string $currency
    *
    * 
    * @param null | bool $active
    *
    * 
    * @param null | bool $casino
    *
    * 
    * @param null | bool $bets
    *
    * 
    * @param null | string $from
    *
    * 
    * @param null | string $to
    *
    * 
    * @param null | float $min_factor
    *
    * 
    * @param object $data
    *
    * 
    * @param int[] $providers_ids
    *
    * 
    * @param int[] $slots_ids
    *
    * required if type is free_spins_for_deposit
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param null | int $segment_id
    *
    * 
    * @param null | bool $is_promo
    *
    * 
    * @param null | bool $is_external
    *
    * required if type is free_spins_for_deposit
    * @param null | int $author_id
    *
    * required if type is free_spins_for_deposit
    * @param null | int $slots_id
    *
    * required if type is free_spins_for_deposit
    * @param null | int $provider_id
    *
    * required if type is free_spins_for_deposit
    * @param null | bool $bonus_balance
    *
    * required if type is free_spins_for_deposit
    * @param null | int $count
    *
    * required if type is free_spins_for_deposit
    * @param null | float $bet
    *
    * required if type is free_spins_for_deposit
    * @param null | string $bet_id
    *
    * required if type is free_spins_for_deposit
    * @param null | float $denomination
    *
    * 
    * @param null | int $max_real_balance
    *
    * 
    * @param null | int $genre_id
    */

    public function __construct(
        public string $name,
        public string $image,
        public object $description,
        public object $condition,
        public object $bonus_name,
        public string $type,
        public array $max_transfers,
        public array $max_bonuses,
        public array $deposit_factors,
        public object $data,
        public array $providers_ids,
        public array $slots_ids,
        public array $trigger_sessions,
        public ?int $min_bet = null,
        public ?int $min_deposit = null,
        public ?int $duration = null,
        public ?float $wager = null,
        public ?string $currency = null,
        public ?bool $active = null,
        public ?bool $casino = null,
        public ?bool $bets = null,
        public ?string $from = null,
        public ?string $to = null,
        public ?float $min_factor = null,
        public ?int $segment_id = null,
        public ?bool $is_promo = null,
        public ?bool $is_external = null,
        public ?int $author_id = null,
        public ?int $slots_id = null,
        public ?int $provider_id = null,
        public ?bool $bonus_balance = null,
        public ?int $count = null,
        public ?float $bet = null,
        public ?string $bet_id = null,
        public ?float $denomination = null,
        public ?int $max_real_balance = null,
        public ?int $genre_id = null,
    ) {}
}

        /**
        * User
        */
        namespace OpenAPI\Client\Model;

        /**
        * User
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class User
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $client_id
    *
    * string(255)
    * @param string $login
    *
    * string(255)
    * @param string $email
    *
    * integer
    * @param null | int $role_id
    *
    * string(45)
    * @param null | string $last_seen_ip
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * datetime
    * @param null | \DateTime $deleted_at
    */

    public function __construct(
        public int $id,
        public int $client_id = 2,
        public string $login,
        public string $email,
        public ?int $role_id = null,
        public ?string $last_seen_ip = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?\DateTime $deleted_at = null,
    ) {}
}

        /**
        * UserBalance
        */
        namespace OpenAPI\Client\Model;

        /**
        * UserBalance
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UserBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $player_id
    *
    * string(3)
    * @param string $currency
    *
    * integer
    * @param null | int $last_transaction_id
    *
    * bigint
    * @param int $balance
    *
    * bigint
    * @param int $debt
    *
    * bigint
    * @param int $deposit
    *
    * bigint
    * @param int $wager
    *
    * bigint
    * @param int $withdraw
    *
    * bigint
    * @param int $in_game
    *
    * bigint
    * @param int $current_payout_limit
    *
    * bigint
    * @param int $total_bet
    *
    * bigint
    * @param int $total_profit
    *
    * bigint
    * @param int $payout_approved
    *
    * bigint
    * @param int $payout_wait
    */

    public function __construct(
        public int $id,
        public int $player_id,
        public string $currency,
        public int $balance,
        public int $debt,
        public int $deposit,
        public int $wager,
        public int $withdraw,
        public int $in_game,
        public int $current_payout_limit,
        public int $total_bet,
        public int $total_profit,
        public int $payout_approved,
        public int $payout_wait,
        public ?int $last_transaction_id = null,
    ) {}
}

