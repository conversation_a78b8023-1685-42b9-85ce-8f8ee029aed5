<?php declare(strict_types=1);

/**
 * <PERSON>vel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * ArrayOfUserObjectsInner
        */
        namespace OpenAPI\Server\Model;

        /**
        * ArrayOfUserObjectsInner
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class ArrayOfUserObjectsInner
{
    /**
    *
    * bigint
    * @param int $id
    */

    public function __construct(
        public int $id,
    ) {}
}

        /**
        * BannerResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * BannerResource
        */
            use <PERSON><PERSON>\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BannerResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(2)
    * @param null | string $language
    *
    * string(3)
    * @param null | string $country
    *
    * string(255)
    * @param string $isForSignedIn
    *
    * string(255)
    * @param string $platform
    *
    * boolean
    * @param bool $mobileApkInstall
    *
    * string(255)
    * @param string $location
    *
    * bigint
    * @param int $weight
    *
    * 
    * @param \OpenAPI\Server\Model\BannerResourceType $type
    *
    * 
    * @param object $disposition
    *
    * string(255)
    * @param string $image
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param null | int $smarticoSegmentId
    *
    * integer
    * @param null | int $startAt
    *
    * integer
    * @param null | int $endAt
    *
    * string(255)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $isForSignedIn,
        public string $platform,
        public bool $mobileApkInstall,
        public string $location,
        public int $weight = 0,
        public \OpenAPI\Server\Model\BannerResourceType $type,
        public object $disposition,
        public string $image,
        public bool $enabled,
        public bool $isActive,
        public ?string $language = null,
        public ?string $country = null,
        public ?int $smarticoSegmentId = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * BannerResourceType
        */
        namespace OpenAPI\Server\Model;

        /**
        * BannerResourceType
            * @description string(255)
        */
            enum BannerResourceType: string
{
        case BIG = 'big';
        case SMALL = 'small';
        case UPPER = 'upper';
}
        /**
        * Bonus
        */
        namespace OpenAPI\Server\Model;

        /**
        * Bonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Bonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * json
    * @param null | string $bonusName
    *
    * text
    * @param null | string $description
    *
    * json
    * @param null | string $condition
    *
    * string(40)
    * @param string $type
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param string $bonuses
    *
    * json
    * @param string $maxTransfers
    *
    * json
    * @param null | string $depositFactors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $minDeposit
    *
    * float
    * @param null | float $minFactor
    *
    * bigint
    * @param null | int $minBet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $totalTransferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $totalUses
    *
    * boolean
    * @param bool $isExternal
    *
    * boolean
    * @param bool $isPromo
    *
    * datetime
    * @param null | \DateTime $activeFrom
    *
    * datetime
    * @param null | \DateTime $activeTil
    *
    * bigint
    * @param null | int $duration
    *
    * text
    * @param string $data
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param int[] $slotProviders
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $activeTriggerSessions
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $type = 'wager',
        public string $bonuses,
        public string $maxTransfers,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $totalTransferred,
        public int $uses,
        public int $transfers,
        public int $totalUses,
        public bool $isExternal = false,
        public bool $isPromo = false,
        public string $data,
        public array $slotProviders,
        public \OpenAPI\Server\Model\FreeSpin $freespin,
        public array $triggerSessions,
        public array $activeTriggerSessions,
        public ?string $bonusName = null,
        public ?string $description = null,
        public ?string $condition = null,
        public ?string $image = null,
        public ?string $depositFactors = null,
        public ?int $minDeposit = null,
        public ?float $minFactor = null,
        public ?int $minBet = null,
        public ?\DateTime $activeFrom = null,
        public ?\DateTime $activeTil = null,
        public ?int $duration = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * BonusBalance
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusBalance
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param null | string $logKey
    *
    * bigint
    * @param int $bonusId
    *
    * string(40)
    * @param string $type
    *
    * bigint
    * @param int $playerId
    *
    * bigint
    * @param int $balance
    *
    * string(255)
    * @param null | string $bonusExternalId
    *
    * string(40)
    * @param string $status
    *
    * bigint
    * @param int $origBonus
    *
    * bigint
    * @param int $origWager
    *
    * bigint
    * @param int $wager
    *
    * float
    * @param null | float $minFactor
    *
    * bigint
    * @param null | int $minBet
    *
    * datetime
    * @param null | \DateTime $expireAt
    *
    * bigint
    * @param int $inGame
    *
    * bigint
    * @param int $transfer
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $popupSeen
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\User $player
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $type = 'wager',
        public int $playerId,
        public int $balance,
        public string $status,
        public int $origBonus,
        public int $origWager,
        public int $wager,
        public int $inGame,
        public int $transfer,
        public string $currency,
        public bool $active,
        public bool $popupSeen = false,
        public bool $casino,
        public bool $bets,
        public \OpenAPI\Server\Model\User $player,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?string $logKey = null,
        public ?string $bonusExternalId = null,
        public ?float $minFactor = null,
        public ?int $minBet = null,
        public ?\DateTime $expireAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * BonusForPlayerResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusForPlayerResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResource
{
    /**
    *
    * 
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * boolean
    * @param bool $crash
    *
    * 
    * @param \OpenAPI\Server\Model\BonusForPlayerResourceMaxBonus $maxBonus
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $depositFactors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $minDeposit
    *
    * bigint
    * @param null | int $minBet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonusData
    *
    * boolean
    * @param bool $isWelcome
    *
    * boolean
    * @param bool $isOnetime
    *
    * boolean
    * @param bool $isNoDep
    *
    * boolean
    * @param bool $isDeposit
    *
    * boolean
    * @param bool $isFreeSpinForDeposit
    *
    * 
    * @param int $weight
    *
    * boolean
    * @param bool $isOrganic
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    */

    public function __construct(
        public int $id,
        public string $name,
        public object $bonusName,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public bool $crash = false,
        public \OpenAPI\Server\Model\BonusForPlayerResourceMaxBonus $maxBonus,
        public array $maxBonuses,
        public array $maxTransfers,
        public array $depositFactors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public object $bonusData,
        public bool $isWelcome,
        public bool $isOnetime,
        public bool $isNoDep,
        public bool $isDeposit,
        public bool $isFreeSpinForDeposit,
        public int $weight,
        public bool $isOrganic,
        public array $triggerSessions,
        public ?string $image = null,
        public ?int $minDeposit = null,
        public ?int $minBet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
    ) {}
}

        /**
        * BonusForPlayerResourceMaxBonus
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusForPlayerResourceMaxBonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResourceMaxBonus
{
    /**
    *
    * 
    * @param string $amount
    *
    * string(3)
    * @param string $currency
    */

    public function __construct(
        public string $amount,
        public string $currency,
    ) {}
}

        /**
        * BonusHistoryLogResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusHistoryLogResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusHistoryLogResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $bonusId
    *
    * 
    * @param string $type
    *
    * 
    * @param string $authorEmail
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $createdAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $type,
        public string $authorEmail,
        public array $data,
        public \DateTime $createdAt,
    ) {}
}

        /**
        * BonusInfo
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | \DateTime $visibleFrom
    *
    * datetime
    * @param null | \DateTime $visibleTo
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $isWelcome
    *
    * boolean
    * @param bool $isForSmartico
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param null | string $bonusName
    *
    * json
    * @param null | string $description
    *
    * json
    * @param null | string $descriptionInfo
    *
    * json
    * @param null | string $conditionTitle
    *
    * json
    * @param null | string $condition
    *
    * json
    * @param null | string $colors
    *
    * integer
    * @param int $sortOrder
    *
    * string(255)
    * @param null | string $proceedLink
    *
    * bigint
    * @param null | int $bonusId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $isForSmartico = false,
        public int $sortOrder,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?\DateTime $visibleFrom = null,
        public ?\DateTime $visibleTo = null,
        public ?bool $isWelcome = false,
        public ?string $image = null,
        public ?string $bonusName = null,
        public ?string $description = null,
        public ?string $descriptionInfo = null,
        public ?string $conditionTitle = null,
        public ?string $condition = null,
        public ?string $colors = null,
        public ?string $proceedLink = null,
        public ?int $bonusId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * BonusInfoResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusInfoResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | int $visibleFrom
    *
    * datetime
    * @param null | int $visibleTo
    *
    * datetime
    * @param null | int $timeFrom
    *
    * datetime
    * @param null | int $timerTo
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $isWelcome
    *
    * boolean
    * @param bool $isForSmartico
    *
    * string(255)
    * @param null | string $image
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param object $description
    *
    * 
    * @param object $descriptionInfo
    *
    * 
    * @param object $conditionTitle
    *
    * 
    * @param object $condition
    *
    * 
    * @param null | string[] $colors
    *
    * 
    * @param \OpenAPI\Server\Model\BonusInfoResourceBonus $bonus
    *
    * boolean
    * @param bool $showVisibleDate
    *
    * boolean
    * @param null | bool $customType
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $isForSmartico = false,
        public object $bonusName,
        public object $description,
        public object $descriptionInfo,
        public object $conditionTitle,
        public object $condition,
        public \OpenAPI\Server\Model\BonusInfoResourceBonus $bonus,
        public bool $showVisibleDate = false,
        public ?int $visibleFrom = null,
        public ?int $visibleTo = null,
        public ?int $timeFrom = null,
        public ?int $timerTo = null,
        public ?bool $isWelcome = false,
        public ?string $image = null,
        public ?array $colors = null,
        public ?bool $customType = null,
    ) {}
}

        /**
        * BonusInfoResourceBonus
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusInfoResourceBonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResourceBonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param int[] $depositFactors
    *
    * 
    * @param null | int $maxBonuses
    */

    public function __construct(
        public int $id,
        public string $type = 'wager',
        public array $depositFactors,
        public ?int $maxBonuses = null,
    ) {}
}

        /**
        * BonusPlayer
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusPlayer
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayer
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * bigint
    * @param int $playerId
    *
    * bigint
    * @param null | int $triggerSessionId
    *
    * string(255)
    * @param string $status
    *
    * integer
    * @param null | int $depositsCount
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public int $playerId,
        public string $status,
        public ?int $triggerSessionId = null,
        public ?int $depositsCount = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * BonusPlayerCard
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusPlayerCard
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayerCard
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $playerId
    *
    * boolean
    * @param bool $isUsed
    *
    * integer
    * @param null | int $bonusId
    *
    * integer
    * @param null | int $bonusInfoId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\User $player
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $playerId,
        public bool $isUsed = false,
        public \OpenAPI\Server\Model\User $player,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?int $bonusId = null,
        public ?int $bonusInfoId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * BonusResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $weight
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $depositFactors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $minDeposit
    *
    * float
    * @param null | float $minFactor
    *
    * bigint
    * @param null | int $minBet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $totalTransferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $totalUses
    *
    * boolean
    * @param bool $isExternal
    *
    * boolean
    * @param bool $isPromo
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonusData
    *
    * boolean
    * @param bool $isWelcome
    *
    * boolean
    * @param bool $isOnetime
    *
    * boolean
    * @param bool $isNoDep
    *
    * boolean
    * @param bool $isDeposit
    *
    * boolean
    * @param bool $isFreeSpinForDeposit
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param int[] $slotProviders
    *
    * 
    * @param int[] $slots
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param null | int $maxRealBalance
    *
    * 
    * @param null | int $genreId
    */

    public function __construct(
        public int $id,
        public int $weight,
        public string $name,
        public object $bonusName,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public array $maxBonuses,
        public array $maxTransfers,
        public array $depositFactors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $totalTransferred,
        public int $uses,
        public int $transfers,
        public int $totalUses,
        public bool $isExternal = false,
        public bool $isPromo = false,
        public object $bonusData,
        public bool $isWelcome,
        public bool $isOnetime,
        public bool $isNoDep,
        public bool $isDeposit,
        public bool $isFreeSpinForDeposit,
        public array $slotProviders,
        public array $slots,
        public \OpenAPI\Server\Model\FreeSpin $freespin,
        public array $triggerSessions,
        public ?string $image = null,
        public ?int $minDeposit = null,
        public ?float $minFactor = null,
        public ?int $minBet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?int $maxRealBalance = null,
        public ?int $genreId = null,
    ) {}
}

        /**
        * BonusTriggerSession
        */
        namespace OpenAPI\Server\Model;

        /**
        * BonusTriggerSession
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusTriggerSession
{
    /**
    *
    * datetime
    * @param \DateTime $startAt
    *
    * datetime
    * @param \DateTime $endAt
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public \DateTime $startAt,
        public \DateTime $endAt,
        public ?string $uuid = null,
    ) {}
}

        /**
        * CreateBonusRequest
        */
        namespace OpenAPI\Server\Model;

        /**
        * CreateBonusRequest
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class CreateBonusRequest
{
    /**
    *
    * 1 - 999
    * @param float $weight
    *
    * 
    * @param string $name
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param \OpenAPI\Server\Model\CreateBonusRequestType $type
    *
    * 
    * @param null | int $minBet
    *
    * 
    * @param null | int $minDeposit
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $depositFactors
    *
    * 
    * @param null | int $duration
    *
    * 
    * @param null | float $wager
    *
    * 
    * @param null | string $currency
    *
    * 
    * @param null | bool $active
    *
    * 
    * @param null | bool $casino
    *
    * 
    * @param null | bool $bets
    *
    * 
    * @param null | string $from
    *
    * 
    * @param null | string $to
    *
    * 
    * @param null | float $minFactor
    *
    * 
    * @param object $data
    *
    * 
    * @param int[] $providersIds
    *
    * 
    * @param int[] $slotsIds
    *
    * required if type is free_spins_for_deposit
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param null | int $segmentId
    *
    * 
    * @param null | bool $isPromo
    *
    * 
    * @param null | bool $isExternal
    *
    * required if type is free_spins_for_deposit
    * @param null | int $authorId
    *
    * required if type is free_spins_for_deposit
    * @param null | int $slotsId
    *
    * required if type is free_spins_for_deposit
    * @param null | int $providerId
    *
    * required if type is free_spins_for_deposit
    * @param null | bool $bonusBalance
    *
    * required if type is free_spins_for_deposit
    * @param null | int $count
    *
    * required if type is free_spins_for_deposit
    * @param null | float $bet
    *
    * required if type is free_spins_for_deposit
    * @param null | string $betId
    *
    * required if type is free_spins_for_deposit
    * @param null | float $denomination
    *
    * 
    * @param null | int $maxRealBalance
    *
    * 
    * @param null | \OpenAPI\Server\Model\CreateBonusRequestGenreId $genreId
    */

    public function __construct(
        public float $weight,
        public string $name,
        public string $image,
        public object $description,
        public object $condition,
        public object $bonusName,
        public \OpenAPI\Server\Model\CreateBonusRequestType $type,
        public array $maxTransfers,
        public array $maxBonuses,
        public array $depositFactors,
        public object $data,
        public array $providersIds,
        public array $slotsIds,
        public array $triggerSessions,
        public ?int $minBet = null,
        public ?int $minDeposit = null,
        public ?int $duration = null,
        public ?float $wager = null,
        public ?string $currency = null,
        public ?bool $active = null,
        public ?bool $casino = null,
        public ?bool $bets = null,
        public ?string $from = null,
        public ?string $to = null,
        public ?float $minFactor = null,
        public ?int $segmentId = null,
        public ?bool $isPromo = null,
        public ?bool $isExternal = null,
        public ?int $authorId = null,
        public ?int $slotsId = null,
        public ?int $providerId = null,
        public ?bool $bonusBalance = null,
        public ?int $count = null,
        public ?float $bet = null,
        public ?string $betId = null,
        public ?float $denomination = null,
        public ?int $maxRealBalance = null,
        public ?\OpenAPI\Server\Model\CreateBonusRequestGenreId $genreId = null,
    ) {}
}

        /**
        * CreateBonusRequestGenreId
        */
        namespace OpenAPI\Server\Model;

        /**
        * CreateBonusRequestGenreId
        */
            enum CreateBonusRequestGenreId: int
{
        case NUMBER_0 = 0;
        case NUMBER_1 = 1;
        case NUMBER_2 = 2;
        case NUMBER_3 = 3;
        case NUMBER_4 = 4;
        case NUMBER_5 = 5;
        case NUMBER_6 = 6;
        case NUMBER_7 = 7;
        case NUMBER_8 = 8;
}
        /**
        * CreateBonusRequestType
        */
        namespace OpenAPI\Server\Model;

        /**
        * CreateBonusRequestType
        */
            enum CreateBonusRequestType: string
{
        case WELCOME = 'welcome';
        case WELCOME_4_STEPS = 'welcome_4_steps';
        case ONETIME = 'onetime';
        case NO_DEP = 'no_dep';
        case DEPOSIT = 'deposit';
        case FREESPIN_BONUS = 'freespin_bonus';
        case FREE_SPINS_FOR_DEPOSIT = 'free_spins_for_deposit';
        case WAGER = 'wager';
        case FREEBET = 'freebet';
        case FREE_MONEY = 'free_money';
        case BET_REFUND = 'bet_refund';
        case NO_RISK = 'no_risk';
}
        /**
        * DataSubscriptions
        */
        namespace OpenAPI\Server\Model;

        /**
        * DataSubscriptions
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DataSubscriptions
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $subId
    *
    * string(255)
    * @param string $accessToken
    *
    * string(255)
    * @param null | string $gameToken
    *
    * string(255)
    * @param string $provider
    *
    * string(255)
    * @param string $providerCompany
    *
    * boolean
    * @param bool $isApproved
    *
    * boolean
    * @param bool $isSlot
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $subId,
        public string $accessToken,
        public string $provider,
        public string $providerCompany,
        public bool $isApproved = false,
        public bool $isSlot = false,
        public ?string $gameToken = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * DepositBonusInfo
        */
        namespace OpenAPI\Server\Model;

        /**
        * DepositBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DepositBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * bigint
    * @param int $smarticoBonusId
    *
    * bigint
    * @param null | int $playerId
    *
    * string(255)
    * @param null | string $email
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public int $smarticoBonusId,
        public string $status = 'in_process',
        public ?int $playerId = null,
        public ?string $email = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * FreeSpin
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpin
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpin
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $slotId
    *
    * bigint
    * @param int $providerId
    *
    * string(255)
    * @param null | string $bonusId
    *
    * string(255)
    * @param string $type
    *
    * string(50)
    * @param string $name
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param null | string $betId
    *
    * string(255)
    * @param null | string $denomination
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * boolean
    * @param bool $isActive
    *
    * bigint
    * @param int $authorId
    *
    * bigint
    * @param null | int $updatedByAuthorId
    *
    * boolean
    * @param bool $inProcess
    *
    * datetime
    * @param null | \DateTime $startAt
    *
    * datetime
    * @param null | \DateTime $expiredAt
    *
    * json
    * @param null | string $data
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpinBound[] $bounds
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    *
    * 
    * @param \OpenAPI\Server\Model\User $author
    *
    * 
    * @param \OpenAPI\Server\Model\User $updateBy
    */

    public function __construct(
        public int $id,
        public int $slotId,
        public int $providerId,
        public string $type = 'manual',
        public string $name,
        public int $count,
        public int $bet,
        public string $currency,
        public string $status = 'active',
        public bool $isActive = false,
        public int $authorId,
        public bool $inProcess = false,
        public array $bounds,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public \OpenAPI\Server\Model\User $author,
        public \OpenAPI\Server\Model\User $updateBy,
        public ?string $bonusId = null,
        public ?string $betId = null,
        public ?string $denomination = null,
        public ?int $updatedByAuthorId = null,
        public ?\DateTime $startAt = null,
        public ?\DateTime $expiredAt = null,
        public ?string $data = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * FreeSpinBound
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinBound
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBound
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $freeSpinId
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonusId
    *
    * bigint
    * @param int $bet
    *
    * string(255)
    * @param string $betId
    *
    * integer
    * @param int $totalWin
    *
    * integer
    * @param int $countLeft
    *
    * boolean
    * @param bool $isActive
    *
    * boolean
    * @param bool $isArchived
    *
    * text(65535)
    * @param null | string $message
    *
    * datetime
    * @param null | float $startAt
    *
    * datetime
    * @param null | float $expireAt
    *
    * datetime
    * @param null | float $canceledAt
    *
    * boolean
    * @param bool $isUserNotified
    *
    * datetime
    * @param null | float $createdAt
    *
    * datetime
    * @param null | float $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\Player $playerInfo
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $uuid,
        public int $bet,
        public string $betId,
        public int $totalWin,
        public int $countLeft,
        public bool $isActive = true,
        public bool $isArchived = false,
        public bool $isUserNotified = false,
        public \OpenAPI\Server\Model\Player $playerInfo,
        public ?int $bonusId = null,
        public ?string $message = null,
        public ?float $startAt = null,
        public ?float $expireAt = null,
        public ?float $canceledAt = null,
        public ?float $createdAt = null,
        public ?float $updatedAt = null,
    ) {}
}

        /**
        * FreeSpinBoundDataResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinBoundDataResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBoundDataResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $freeSpinId
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonusId
    *
    * bigint
    * @param int $betId
    *
    * bigint
    * @param int $bet
    *
    * integer
    * @param int $totalWin
    *
    * integer
    * @param int $countLeft
    *
    * string
    * @param string $message
    *
    * boolean
    * @param bool $isActive
    *
    * boolean
    * @param bool $isArchived
    *
    * boolean
    * @param bool $isUserNotified
    *
    * datetime
    * @param null | float $startAt
    *
    * datetime
    * @param null | float $expireAt
    *
    * datetime
    * @param null | float $canceledAt
    *
    * datetime
    * @param null | float $createdAt
    *
    * datetime
    * @param null | float $updatedAt
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $uuid,
        public int $betId,
        public int $bet,
        public int $totalWin,
        public int $countLeft,
        public string $message,
        public bool $isActive = true,
        public bool $isArchived = false,
        public bool $isUserNotified = false,
        public ?int $bonusId = null,
        public ?float $startAt = null,
        public ?float $expireAt = null,
        public ?float $canceledAt = null,
        public ?float $createdAt = null,
        public ?float $updatedAt = null,
    ) {}
}

        /**
        * FreeSpinBoundResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinBoundResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBoundResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $freeSpinId
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonusId
    *
    * bigint
    * @param int $bet
    *
    * integer
    * @param int $totalWin
    *
    * integer
    * @param int $countLeft
    *
    * boolean
    * @param bool $isActive
    *
    * boolean
    * @param bool $isArchived
    *
    * datetime
    * @param null | float $startAt
    *
    * datetime
    * @param null | float $expireAt
    *
    * datetime
    * @param null | float $canceledAt
    *
    * datetime
    * @param null | float $createdAt
    *
    * datetime
    * @param null | float $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\FreeSpinResource $freespin
    *
    * 
    * @param int $slotId
    *
    * 
    * @param \OpenAPI\Server\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $uuid,
        public int $bet,
        public int $totalWin,
        public int $countLeft,
        public bool $isActive = true,
        public bool $isArchived = false,
        public \OpenAPI\Server\Model\FreeSpinResource $freespin,
        public int $slotId,
        public \OpenAPI\Server\Model\BonusResource $bonus,
        public ?int $bonusId = null,
        public ?float $startAt = null,
        public ?float $expireAt = null,
        public ?float $canceledAt = null,
        public ?float $createdAt = null,
        public ?float $updatedAt = null,
    ) {}
}

        /**
        * FreeSpinHistoryLogResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinHistoryLogResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinHistoryLogResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $freeSpinId
    *
    * 
    * @param string $type
    *
    * 
    * @param string $authorEmail
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $createdAt
    */

    public function __construct(
        public int $id,
        public int $freeSpinId,
        public string $type,
        public string $authorEmail,
        public array $data,
        public \DateTime $createdAt,
    ) {}
}

        /**
        * FreeSpinResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * FreeSpinResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $generalProviderId
    *
    * bigint
    * @param null | int $aggregatorId
    *
    * bigint
    * @param int $slotId
    *
    * bigint
    * @param int $providerId
    *
    * bigint
    * @param int $bonusId
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $productName
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param string $betId
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * 
    * @param null | float $denomination
    *
    * boolean
    * @param bool $isActive
    *
    * bigint
    * @param int $authorId
    *
    * bigint
    * @param null | int $updatedByAuthorId
    *
    * boolean
    * @param bool $inProcess
    *
    * datetime
    * @param null | \DateTime $startAt
    *
    * datetime
    * @param null | \DateTime $expiredAt
    *
    * 
    * @param object $data
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param array[] $users
    *
    * 
    * @param \OpenAPI\Server\Model\User $author
    *
    * 
    * @param \OpenAPI\Server\Model\User $updateBy
    */

    public function __construct(
        public int $id,
        public int $generalProviderId,
        public int $slotId,
        public int $providerId,
        public int $bonusId,
        public string $type = 'manual',
        public string $name,
        public string $productName,
        public int $count,
        public int $bet,
        public string $betId = '0',
        public string $currency,
        public string $status = 'active',
        public bool $isActive = false,
        public int $authorId,
        public bool $inProcess = false,
        public object $data,
        public array $users,
        public \OpenAPI\Server\Model\User $author,
        public \OpenAPI\Server\Model\User $updateBy,
        public ?int $aggregatorId = null,
        public ?float $denomination = null,
        public ?int $updatedByAuthorId = null,
        public ?\DateTime $startAt = null,
        public ?\DateTime $expiredAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * MassFreeBetBonusInfo
        */
        namespace OpenAPI\Server\Model;

        /**
        * MassFreeBetBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class MassFreeBetBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * string(255)
    * @param null | string $smarticoBonusId
    *
    * bigint
    * @param null | int $templateId
    *
    * string(255)
    * @param null | string $externalPlayerId
    *
    * string(255)
    * @param string $currency
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * bigint
    * @param null | int $playerId
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $currency,
        public string $status = 'in_process',
        public ?string $smarticoBonusId = null,
        public ?int $templateId = null,
        public ?string $externalPlayerId = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?int $playerId = null,
    ) {}
}

        /**
        * MassOnetimeBonusInfo
        */
        namespace OpenAPI\Server\Model;

        /**
        * MassOnetimeBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class MassOnetimeBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonusId
    *
    * string(255)
    * @param null | string $smarticoBonusId
    *
    * bigint
    * @param null | int $playerId
    *
    * string(255)
    * @param null | string $email
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $bonusId,
        public string $status = 'in_process',
        public ?string $smarticoBonusId = null,
        public ?int $playerId = null,
        public ?string $email = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * NoContent200
        */
        namespace OpenAPI\Server\Model;

        /**
        * NoContent200
            * @description No content for 200
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class NoContent200
{
    /**
    *
    * dummy property for no-content responses
    * @param null | string $dummy
    */

    public function __construct(
        public ?string $dummy = null,
    ) {}
}

        /**
        * NoContent401
        */
        namespace OpenAPI\Server\Model;

        /**
        * NoContent401
            * @description No content for 401
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class NoContent401
{
    /**
    *
    * dummy property for no-content responses
    * @param null | string $dummy
    */

    public function __construct(
        public ?string $dummy = null,
    ) {}
}

        /**
        * PaginationFirstAndLastLinks
        */
        namespace OpenAPI\Server\Model;

        /**
        * PaginationFirstAndLastLinks
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationFirstAndLastLinks
{
    /**
    *
    * First
    * @param string $first
    *
    * Last
    * @param string $last
    *
    * Previous
    * @param null | string $prev
    *
    * Next
    * @param string $next
    */

    public function __construct(
        public string $first,
        public string $last,
        public string $next,
        public ?string $prev = null,
    ) {}
}

        /**
        * PaginationLinksInner
        */
        namespace OpenAPI\Server\Model;

        /**
        * PaginationLinksInner
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationLinksInner
{
    /**
    *
    * URL
    * @param string $url
    *
    * Label
    * @param float $label
    *
    * Active
    * @param bool $active
    */

    public function __construct(
        public string $url,
        public float $label,
        public bool $active,
    ) {}
}

        /**
        * PaginationMeta
        */
        namespace OpenAPI\Server\Model;

        /**
        * PaginationMeta
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PaginationMeta
{
    /**
    *
    * Current page
    * @param int $currentPage
    *
    * From
    * @param int $from
    *
    * Last page
    * @param int $lastPage
    *
    * 
    * @param \OpenAPI\Server\Model\PaginationLinksInner[] $links
    *
    * Path
    * @param string $path
    *
    * Items per page
    * @param int $perPage
    *
    * Total pages
    * @param int $to
    *
    * Total items
    * @param int $total
    */

    public function __construct(
        public int $currentPage = 1,
        public int $from = 1,
        public int $lastPage = 11,
        public array $links,
        public string $path = 'http://localhost:8000/api/bonuses',
        public int $perPage = 15,
        public int $to,
        public int $total,
    ) {}
}

        /**
        * Player
        */
        namespace OpenAPI\Server\Model;

        /**
        * Player
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Player
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param null | string $username
    *
    * string(255)
    * @param string $password
    *
    * string(255)
    * @param null | string $email
    *
    * boolean
    * @param bool $isUnderModeration
    *
    * boolean
    * @param bool $payoutWithoutModeration
    *
    * boolean
    * @param bool $subscribed
    *
    * integer
    * @param null | int $bonusBalanceId
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $emailVerifiedAt
    *
    * string(255)
    * @param null | string $phone
    *
    * string(6)
    * @param null | string $phoneCode
    *
    * datetime
    * @param null | \DateTime $phoneVerifiedAt
    *
    * string(255)
    * @param string $firstName
    *
    * string(255)
    * @param null | string $lastName
    *
    * date
    * @param null | \DateTime $birth
    *
    * string(2)
    * @param null | string $country
    *
    * string(255)
    * @param null | string $city
    *
    * string(7)
    * @param null | string $gender
    *
    * string(2)
    * @param null | string $language
    *
    * string(255)
    * @param null | string $clickId
    *
    * string(3)
    * @param string $currency
    *
    * string(45)
    * @param null | string $lastSeenIp
    *
    * boolean
    * @param bool $blocked
    *
    * text(65535)
    * @param null | string $comment
    *
    * integer
    * @param null | int $welcomeBonusId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $password,
        public bool $isUnderModeration = false,
        public bool $payoutWithoutModeration = false,
        public bool $subscribed = false,
        public string $uuid,
        public string $firstName,
        public string $currency,
        public bool $blocked = false,
        public ?string $username = null,
        public ?string $email = null,
        public ?int $bonusBalanceId = null,
        public ?\DateTime $emailVerifiedAt = null,
        public ?string $phone = null,
        public ?string $phoneCode = null,
        public ?\DateTime $phoneVerifiedAt = null,
        public ?string $lastName = null,
        public ?\DateTime $birth = null,
        public ?string $country = null,
        public ?string $city = null,
        public ?string $gender = null,
        public ?string $language = null,
        public ?string $clickId = null,
        public ?string $lastSeenIp = null,
        public ?string $comment = null,
        public ?int $welcomeBonusId = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * PromoCode
        */
        namespace OpenAPI\Server\Model;

        /**
        * PromoCode
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCode
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $streamId
    *
    * bigint
    * @param int $bonusId
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $useLimit
    *
    * datetime
    * @param null | int $startAt
    *
    * datetime
    * @param null | int $endAt
    *
    * string
    * @param object $condition
    *
    * boolean
    * @param bool $isAlanbase
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * 
    * @param \OpenAPI\Server\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $code,
        public int $bonusId,
        public bool $isActive = false,
        public int $uses,
        public int $useLimit,
        public object $condition,
        public bool $isAlanbase = false,
        public string $uuid,
        public \OpenAPI\Server\Model\Bonus $bonus,
        public ?int $streamId = null,
        public ?string $description = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * PromoCodeResource
        */
        namespace OpenAPI\Server\Model;

        /**
        * PromoCodeResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCodeResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $streamId
    *
    * bigint
    * @param int $bonusId
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $limit
    *
    * datetime
    * @param null | int $startAt
    *
    * datetime
    * @param null | int $endAt
    *
    * 
    * @param object $condition
    *
    * boolean
    * @param bool $isAlanbase
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $name,
        public string $code,
        public int $bonusId,
        public bool $active = false,
        public int $uses,
        public int $limit,
        public object $condition,
        public bool $isAlanbase = false,
        public string $uuid,
        public \OpenAPI\Server\Model\BonusResource $bonus,
        public ?int $streamId = null,
        public ?string $description = null,
        public ?int $startAt = null,
        public ?int $endAt = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
    ) {}
}

        /**
        * Slot
        */
        namespace OpenAPI\Server\Model;

        /**
        * Slot
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Slot
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * string(255)
    * @param string $provider
    *
    * integer
    * @param int $version
    *
    * bigint
    * @param int $externalProviderId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $slug
    *
    * string(255)
    * @param string $internalId
    *
    * string(255)
    * @param string $externalId
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $suspended
    *
    * text
    * @param string $meta
    *
    * string(255)
    * @param string $image
    *
    * text(16777215)
    * @param null | string $description
    *
    * boolean
    * @param bool $isMobile
    *
    * boolean
    * @param bool $isBonusReady
    *
    * boolean
    * @param bool $isWagerReady
    *
    * boolean
    * @param bool $isDesktop
    *
    * boolean
    * @param bool $hasLobby
    *
    * boolean
    * @param bool $hasFreespins
    *
    * boolean
    * @param null | bool $isDemo
    *
    * integer
    * @param int $genreId
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * datetime
    * @param null | \DateTime $deletedAt
    *
    * string(255)
    * @param string $friendlyUrl
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public string $provider,
        public int $version,
        public int $externalProviderId,
        public string $name,
        public string $slug,
        public string $internalId,
        public string $externalId,
        public bool $enabled,
        public bool $suspended,
        public string $meta,
        public string $image,
        public bool $isMobile,
        public bool $isBonusReady = false,
        public bool $isWagerReady = false,
        public bool $isDesktop,
        public bool $hasLobby,
        public bool $hasFreespins = false,
        public int $genreId,
        public string $friendlyUrl,
        public ?string $description = null,
        public ?bool $isDemo = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?\DateTime $deletedAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * SlotsProvider
        */
        namespace OpenAPI\Server\Model;

        /**
        * SlotsProvider
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class SlotsProvider
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $clientId
    *
    * bigint
    * @param int $subscriptionId
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param null | string $customName
    *
    * string(255)
    * @param null | string $image
    *
    * boolean
    * @param bool $suspended
    *
    * boolean
    * @param bool $isBonusBarred
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Server\Model\Slot[] $getProviders
    */

    public function __construct(
        public int $id,
        public int $clientId,
        public int $subscriptionId,
        public string $name,
        public bool $suspended = false,
        public bool $isBonusBarred = false,
        public array $getProviders,
        public ?string $customName = null,
        public ?string $image = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?string $uuid = null,
    ) {}
}

        /**
        * Template
        */
        namespace OpenAPI\Server\Model;

        /**
        * Template
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Template
{
    /**
    *
    * bigint
    * @param int $version
    *
    * string(255)
    * @param string $id
    *
    * text
    * @param null | string $name
    *
    * boolean
    * @param bool $isActive
    *
    * integer
    * @param int $maxBonusNumber
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $operatorId
    *
    * string(255)
    * @param string $brandId
    *
    * datetime
    * @param null | string $eventScheduled
    *
    * datetime
    * @param null | float $fromTime
    *
    * datetime
    * @param null | float $toTime
    *
    * 
    * @param float $daysToUse
    *
    * text
    * @param null | string $eventsAvailability
    *
    * text
    * @param null | string $restrictions
    *
    * 
    * @param \OpenAPI\Server\Model\TemplateFreebetData $freebetData
    */

    public function __construct(
        public int $version,
        public string $id,
        public bool $isActive = false,
        public int $maxBonusNumber,
        public string $type,
        public string $operatorId,
        public string $brandId,
        public float $daysToUse,
        public \OpenAPI\Server\Model\TemplateFreebetData $freebetData,
        public ?string $name = null,
        public ?string $eventScheduled = null,
        public ?float $fromTime = null,
        public ?float $toTime = null,
        public ?string $eventsAvailability = null,
        public ?string $restrictions = null,
    ) {}
}

        /**
        * TemplateFreebetData
        */
        namespace OpenAPI\Server\Model;

        /**
        * TemplateFreebetData
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetData
{
    /**
    *
    * bigint
    * @param int $version
    *
    * boolean
    * @param bool $isApiAmount
    *
    * 
    * @param int[] $amountList
    *
    * string(255)
    * @param string $type
    *
    * integer
    * @param int $minSelection
    *
    * integer
    * @param int $maxSelection
    *
    * 
    * @param int $minOdd
    *
    * 
    * @param int $maxOdd
    *
    * 
    * @param bool $boundRefund
    *
    * 
    * @param object $description
    *
    * string(255)
    * @param null | string $reserveTemplate
    *
    * 
    * @param \OpenAPI\Server\Model\TemplateFreebetDataBetRestrictions $betRestrictions
    */

    public function __construct(
        public int $version,
        public bool $isApiAmount = false,
        public array $amountList,
        public string $type,
        public int $minSelection,
        public int $maxSelection,
        public int $minOdd,
        public int $maxOdd,
        public bool $boundRefund,
        public object $description,
        public \OpenAPI\Server\Model\TemplateFreebetDataBetRestrictions $betRestrictions,
        public ?string $reserveTemplate = null,
    ) {}
}

        /**
        * TemplateFreebetDataBetRestrictions
        */
        namespace OpenAPI\Server\Model;

        /**
        * TemplateFreebetDataBetRestrictions
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetDataBetRestrictions
{
    /**
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param string[] $betsData
    */

    public function __construct(
        public string $type,
        public array $betsData,
    ) {}
}

        /**
        * UpdateBonusRequest
        */
        namespace OpenAPI\Server\Model;

        /**
        * UpdateBonusRequest
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UpdateBonusRequest
{
    /**
    *
    * 
    * @param string $name
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $bonusName
    *
    * 
    * @param \OpenAPI\Server\Model\CreateBonusRequestType $type
    *
    * 
    * @param null | int $minBet
    *
    * 
    * @param null | int $minDeposit
    *
    * 
    * @param int[] $maxTransfers
    *
    * 
    * @param int[] $maxBonuses
    *
    * 
    * @param int[] $depositFactors
    *
    * 
    * @param null | int $duration
    *
    * 
    * @param null | float $wager
    *
    * 
    * @param null | string $currency
    *
    * 
    * @param null | bool $active
    *
    * 
    * @param null | bool $casino
    *
    * 
    * @param null | bool $bets
    *
    * 
    * @param null | string $from
    *
    * 
    * @param null | string $to
    *
    * 
    * @param null | float $minFactor
    *
    * 
    * @param object $data
    *
    * 
    * @param int[] $providersIds
    *
    * 
    * @param int[] $slotsIds
    *
    * required if type is free_spins_for_deposit
    * @param \OpenAPI\Server\Model\BonusTriggerSession[] $triggerSessions
    *
    * 
    * @param null | int $segmentId
    *
    * 
    * @param null | bool $isPromo
    *
    * 
    * @param null | bool $isExternal
    *
    * required if type is free_spins_for_deposit
    * @param null | int $authorId
    *
    * required if type is free_spins_for_deposit
    * @param null | int $slotsId
    *
    * required if type is free_spins_for_deposit
    * @param null | int $providerId
    *
    * required if type is free_spins_for_deposit
    * @param null | bool $bonusBalance
    *
    * required if type is free_spins_for_deposit
    * @param null | int $count
    *
    * required if type is free_spins_for_deposit
    * @param null | float $bet
    *
    * required if type is free_spins_for_deposit
    * @param null | string $betId
    *
    * required if type is free_spins_for_deposit
    * @param null | float $denomination
    *
    * 
    * @param null | int $maxRealBalance
    *
    * 
    * @param null | \OpenAPI\Server\Model\CreateBonusRequestGenreId $genreId
    */

    public function __construct(
        public string $name,
        public string $image,
        public object $description,
        public object $condition,
        public object $bonusName,
        public \OpenAPI\Server\Model\CreateBonusRequestType $type,
        public array $maxTransfers,
        public array $maxBonuses,
        public array $depositFactors,
        public object $data,
        public array $providersIds,
        public array $slotsIds,
        public array $triggerSessions,
        public ?int $minBet = null,
        public ?int $minDeposit = null,
        public ?int $duration = null,
        public ?float $wager = null,
        public ?string $currency = null,
        public ?bool $active = null,
        public ?bool $casino = null,
        public ?bool $bets = null,
        public ?string $from = null,
        public ?string $to = null,
        public ?float $minFactor = null,
        public ?int $segmentId = null,
        public ?bool $isPromo = null,
        public ?bool $isExternal = null,
        public ?int $authorId = null,
        public ?int $slotsId = null,
        public ?int $providerId = null,
        public ?bool $bonusBalance = null,
        public ?int $count = null,
        public ?float $bet = null,
        public ?string $betId = null,
        public ?float $denomination = null,
        public ?int $maxRealBalance = null,
        public ?\OpenAPI\Server\Model\CreateBonusRequestGenreId $genreId = null,
    ) {}
}

        /**
        * User
        */
        namespace OpenAPI\Server\Model;

        /**
        * User
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class User
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $clientId
    *
    * string(255)
    * @param string $login
    *
    * string(255)
    * @param string $email
    *
    * integer
    * @param null | int $roleId
    *
    * string(45)
    * @param null | string $lastSeenIp
    *
    * datetime
    * @param null | \DateTime $createdAt
    *
    * datetime
    * @param null | \DateTime $updatedAt
    *
    * datetime
    * @param null | \DateTime $deletedAt
    */

    public function __construct(
        public int $id,
        public int $clientId = 2,
        public string $login,
        public string $email,
        public ?int $roleId = null,
        public ?string $lastSeenIp = null,
        public ?\DateTime $createdAt = null,
        public ?\DateTime $updatedAt = null,
        public ?\DateTime $deletedAt = null,
    ) {}
}

        /**
        * UserBalance
        */
        namespace OpenAPI\Server\Model;

        /**
        * UserBalance
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UserBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $playerId
    *
    * string(3)
    * @param string $currency
    *
    * integer
    * @param null | int $lastTransactionId
    *
    * bigint
    * @param int $balance
    *
    * bigint
    * @param int $debt
    *
    * bigint
    * @param int $deposit
    *
    * bigint
    * @param int $wager
    *
    * bigint
    * @param int $withdraw
    *
    * bigint
    * @param int $inGame
    *
    * bigint
    * @param int $currentPayoutLimit
    *
    * bigint
    * @param int $totalBet
    *
    * bigint
    * @param int $totalProfit
    *
    * bigint
    * @param int $payoutApproved
    *
    * bigint
    * @param int $payoutWait
    */

    public function __construct(
        public int $id,
        public int $playerId,
        public string $currency,
        public int $balance,
        public int $debt,
        public int $deposit,
        public int $wager,
        public int $withdraw,
        public int $inGame,
        public int $currentPayoutLimit,
        public int $totalBet,
        public int $totalProfit,
        public int $payoutApproved,
        public int $payoutWait,
        public ?int $lastTransactionId = null,
    ) {}
}

