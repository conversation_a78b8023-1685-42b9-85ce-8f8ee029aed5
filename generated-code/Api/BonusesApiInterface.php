<?php declare(strict_types=1);

/**
 * <PERSON>vel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


namespace OpenAPI\Server\Api;


interface BonusesApiInterface {


    /**
     * Operation createBonus
     *
     * 
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @param null | \OpenAPI\Server\Model\CreateBonusRequest $createBonusRequest
     * @return \OpenAPI\Server\Model\BonusResource | \OpenAPI\Server\Model\NoContent401
     */
    public function createBonus(
            ?string $xSignature,
            ?string $xClusterConnection,
            ?\OpenAPI\Server\Model\CreateBonusRequest $createBonusRequest,
    ):
        \OpenAPI\Server\Model\BonusResource | 
        \OpenAPI\Server\Model\NoContent401
    ;


    /**
     * Operation deleteBonus
     *
     * 
     * @param string $id
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @return \OpenAPI\Server\Model\NoContent200 | \OpenAPI\Server\Model\NoContent401
     */
    public function deleteBonus(
            string $id,
            ?string $xSignature,
            ?string $xClusterConnection,
    ):
        \OpenAPI\Server\Model\NoContent200 | 
        \OpenAPI\Server\Model\NoContent401
    ;


    /**
     * Operation updateBonus
     *
     * 
     * @param string $id
     * @param null | string $xSignature
     * @param null | string $xClusterConnection
     * @param null | \OpenAPI\Server\Model\UpdateBonusRequest $updateBonusRequest
     * @return \OpenAPI\Server\Model\BonusResource | \OpenAPI\Server\Model\NoContent401
     */
    public function updateBonus(
            string $id,
            ?string $xSignature,
            ?string $xClusterConnection,
            ?\OpenAPI\Server\Model\UpdateBonusRequest $updateBonusRequest,
    ):
        \OpenAPI\Server\Model\BonusResource | 
        \OpenAPI\Server\Model\NoContent401
    ;

}
