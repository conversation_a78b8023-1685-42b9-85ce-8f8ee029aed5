<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


use Illuminate\Support\Facades\Route;

/**
 * POST createBonus
 * Summary: 
 * Notes: create bonus
 */
Route::POST('/api/api/bonuses/add', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'createBonus'])->name('bonuses.create.bonus');

/**
 * DELETE deleteBonus
 * Summary: 
 * Notes: delete bonus
 */
Route::DELETE('/api/api/bonuses/{id}', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'deleteBonus'])->name('bonuses.delete.bonus');

/**
 * PUT updateBonus
 * Summary: 
 * Notes: update bonus
 */
Route::PUT('/api/api/bonuses/{id}', [\OpenAPI\Server\Http\Controllers\BonusesController::class, 'updateBonus'])->name('bonuses.update.bonus');

