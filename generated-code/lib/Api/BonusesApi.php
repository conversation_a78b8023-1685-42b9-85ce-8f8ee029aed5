<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


namespace OpenAPI\Client\Api;


interface BonusesApi {


    /**
     * Operation createBonus
     *
     * 
     * @param null | string $x_signature
     * @param null | string $x_cluster_connection
     * @param null | \OpenAPI\Client\Model\CreateBonusRequest $create_bonus_request
     * @return \OpenAPI\Client\Model\BonusResource | 
     */
    public function createBonus(
            ?string $x_signature,
            ?string $x_cluster_connection,
            ?\OpenAPI\Client\Model\CreateBonusRequest $create_bonus_request,
    ):
        \OpenAPI\Client\Model\BonusResource | 
    ;


    /**
     * Operation deleteBonus
     *
     * 
     * @param string $id
     * @param null | string $x_signature
     * @param null | string $x_cluster_connection
     * @return  | 
     */
    public function deleteBonus(
            string $id,
            ?string $x_signature,
            ?string $x_cluster_connection,
    ):
    ;


    /**
     * Operation updateBonus
     *
     * 
     * @param string $id
     * @param null | string $x_signature
     * @param null | string $x_cluster_connection
     * @param null | \OpenAPI\Client\Model\UpdateBonusRequest $update_bonus_request
     * @return \OpenAPI\Client\Model\BonusResource | 
     */
    public function updateBonus(
            string $id,
            ?string $x_signature,
            ?string $x_cluster_connection,
            ?\OpenAPI\Client\Model\UpdateBonusRequest $update_bonus_request,
    ):
        \OpenAPI\Client\Model\BonusResource | 
    ;

}
