<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * MassFreeBetBonusInfo
        */
        namespace OpenAPI\Client\Model;

        /**
        * MassFreeBetBonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class MassFreeBetBonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonus_id
    *
    * string(255)
    * @param null | string $smartico_bonus_id
    *
    * bigint
    * @param null | int $template_id
    *
    * string(255)
    * @param null | string $external_player_id
    *
    * string(255)
    * @param string $currency
    *
    * bigint
    * @param null | int $amount
    *
    * string(255)
    * @param string $status
    *
    * text(65535)
    * @param null | string $text
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * bigint
    * @param null | int $player_id
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public string $currency,
        public string $status = 'in_process',
        public ?string $smartico_bonus_id = null,
        public ?int $template_id = null,
        public ?string $external_player_id = null,
        public ?int $amount = null,
        public ?string $text = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?int $player_id = null,
    ) {}
}

