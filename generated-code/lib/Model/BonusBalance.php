<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusBalance
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusBalance
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param null | string $log_key
    *
    * bigint
    * @param int $bonus_id
    *
    * string(40)
    * @param string $type
    *
    * bigint
    * @param int $player_id
    *
    * bigint
    * @param int $balance
    *
    * string(255)
    * @param null | string $bonus_external_id
    *
    * string(40)
    * @param string $status
    *
    * bigint
    * @param int $orig_bonus
    *
    * bigint
    * @param int $orig_wager
    *
    * bigint
    * @param int $wager
    *
    * float
    * @param null | float $min_factor
    *
    * bigint
    * @param null | int $min_bet
    *
    * datetime
    * @param null | \DateTime $expire_at
    *
    * bigint
    * @param int $in_game
    *
    * bigint
    * @param int $transfer
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $popup_seen
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\User $player
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public string $type = 'wager',
        public int $player_id,
        public int $balance,
        public string $status,
        public int $orig_bonus,
        public int $orig_wager,
        public int $wager,
        public int $in_game,
        public int $transfer,
        public string $currency,
        public bool $active,
        public bool $popup_seen = false,
        public bool $casino,
        public bool $bets,
        public \OpenAPI\Client\Model\User $player,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?string $log_key = null,
        public ?string $bonus_external_id = null,
        public ?float $min_factor = null,
        public ?int $min_bet = null,
        public ?\DateTime $expire_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

