<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * UpdateBonusRequest
        */
        namespace OpenAPI\Client\Model;

        /**
        * UpdateBonusRequest
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UpdateBonusRequest
{
    /**
    *
    * 
    * @param string $name
    *
    * 
    * @param string $image
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param string $type
    *
    * 
    * @param null | int $min_bet
    *
    * 
    * @param null | int $min_deposit
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $deposit_factors
    *
    * 
    * @param null | int $duration
    *
    * 
    * @param null | float $wager
    *
    * 
    * @param null | string $currency
    *
    * 
    * @param null | bool $active
    *
    * 
    * @param null | bool $casino
    *
    * 
    * @param null | bool $bets
    *
    * 
    * @param null | string $from
    *
    * 
    * @param null | string $to
    *
    * 
    * @param null | float $min_factor
    *
    * 
    * @param object $data
    *
    * 
    * @param int[] $providers_ids
    *
    * 
    * @param int[] $slots_ids
    *
    * required if type is free_spins_for_deposit
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param null | int $segment_id
    *
    * 
    * @param null | bool $is_promo
    *
    * 
    * @param null | bool $is_external
    *
    * required if type is free_spins_for_deposit
    * @param null | int $author_id
    *
    * required if type is free_spins_for_deposit
    * @param null | int $slots_id
    *
    * required if type is free_spins_for_deposit
    * @param null | int $provider_id
    *
    * required if type is free_spins_for_deposit
    * @param null | bool $bonus_balance
    *
    * required if type is free_spins_for_deposit
    * @param null | int $count
    *
    * required if type is free_spins_for_deposit
    * @param null | float $bet
    *
    * required if type is free_spins_for_deposit
    * @param null | string $bet_id
    *
    * required if type is free_spins_for_deposit
    * @param null | float $denomination
    *
    * 
    * @param null | int $max_real_balance
    *
    * 
    * @param null | int $genre_id
    */

    public function __construct(
        public string $name,
        public string $image,
        public object $description,
        public object $condition,
        public object $bonus_name,
        public string $type,
        public array $max_transfers,
        public array $max_bonuses,
        public array $deposit_factors,
        public object $data,
        public array $providers_ids,
        public array $slots_ids,
        public array $trigger_sessions,
        public ?int $min_bet = null,
        public ?int $min_deposit = null,
        public ?int $duration = null,
        public ?float $wager = null,
        public ?string $currency = null,
        public ?bool $active = null,
        public ?bool $casino = null,
        public ?bool $bets = null,
        public ?string $from = null,
        public ?string $to = null,
        public ?float $min_factor = null,
        public ?int $segment_id = null,
        public ?bool $is_promo = null,
        public ?bool $is_external = null,
        public ?int $author_id = null,
        public ?int $slots_id = null,
        public ?int $provider_id = null,
        public ?bool $bonus_balance = null,
        public ?int $count = null,
        public ?float $bet = null,
        public ?string $bet_id = null,
        public ?float $denomination = null,
        public ?int $max_real_balance = null,
        public ?int $genre_id = null,
    ) {}
}

