<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusPlayer
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusPlayer
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayer
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $bonus_id
    *
    * bigint
    * @param int $player_id
    *
    * bigint
    * @param null | int $trigger_session_id
    *
    * string(255)
    * @param string $status
    *
    * integer
    * @param null | int $deposits_count
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    */

    public function __construct(
        public int $id,
        public int $bonus_id,
        public int $player_id,
        public string $status,
        public ?int $trigger_session_id = null,
        public ?int $deposits_count = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

