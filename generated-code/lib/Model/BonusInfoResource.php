<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusInfoResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusInfoResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON><PERSON>\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfoResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | int $visible_from
    *
    * datetime
    * @param null | int $visible_to
    *
    * datetime
    * @param null | int $time_from
    *
    * datetime
    * @param null | int $timer_to
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $is_welcome
    *
    * boolean
    * @param bool $is_for_smartico
    *
    * string(255)
    * @param null | string $image
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param object $description
    *
    * 
    * @param object $description_info
    *
    * 
    * @param object $condition_title
    *
    * 
    * @param object $condition
    *
    * 
    * @param null | string[] $colors
    *
    * 
    * @param \OpenAPI\Client\Model\BonusInfoResourceBonus $bonus
    *
    * boolean
    * @param bool $show_visible_date
    *
    * boolean
    * @param null | bool $custom_type
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $is_for_smartico = false,
        public object $bonus_name,
        public object $description,
        public object $description_info,
        public object $condition_title,
        public object $condition,
        public \OpenAPI\Client\Model\BonusInfoResourceBonus $bonus,
        public bool $show_visible_date = false,
        public ?int $visible_from = null,
        public ?int $visible_to = null,
        public ?int $time_from = null,
        public ?int $timer_to = null,
        public ?bool $is_welcome = false,
        public ?string $image = null,
        public ?array $colors = null,
        public ?bool $custom_type = null,
    ) {}
}

