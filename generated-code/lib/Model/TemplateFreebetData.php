<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * TemplateFreebetData
        */
        namespace OpenAPI\Client\Model;

        /**
        * TemplateFreebetData
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class TemplateFreebetData
{
    /**
    *
    * bigint
    * @param int $__version__
    *
    * boolean
    * @param bool $is_api_amount
    *
    * 
    * @param int[] $amount_list
    *
    * string(255)
    * @param string $type
    *
    * integer
    * @param int $min_selection
    *
    * integer
    * @param int $max_selection
    *
    * 
    * @param int $min_odd
    *
    * 
    * @param int $max_odd
    *
    * 
    * @param bool $bound_refund
    *
    * 
    * @param object $description
    *
    * string(255)
    * @param null | string $reserve_template
    *
    * 
    * @param \OpenAPI\Client\Model\TemplateFreebetDataBetRestrictions $bet_restrictions
    */

    public function __construct(
        public int $__version__,
        public bool $is_api_amount = false,
        public array $amount_list,
        public string $type,
        public int $min_selection,
        public int $max_selection,
        public int $min_odd,
        public int $max_odd,
        public bool $bound_refund,
        public object $description,
        public \OpenAPI\Client\Model\TemplateFreebetDataBetRestrictions $bet_restrictions,
        public ?string $reserve_template = null,
    ) {}
}

