<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * FreeSpin
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpin
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpin
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $slot_id
    *
    * bigint
    * @param int $provider_id
    *
    * string(255)
    * @param null | string $bonus_id
    *
    * string(255)
    * @param string $type
    *
    * string(50)
    * @param string $name
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param null | string $bet_id
    *
    * string(255)
    * @param null | string $denomination
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * boolean
    * @param bool $is_active
    *
    * bigint
    * @param int $author_id
    *
    * bigint
    * @param null | int $updated_by_author_id
    *
    * boolean
    * @param bool $in_process
    *
    * datetime
    * @param null | \DateTime $start_at
    *
    * datetime
    * @param null | \DateTime $expired_at
    *
    * json
    * @param null | string $data
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpinBound[] $bounds
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    *
    * 
    * @param \OpenAPI\Client\Model\User $author
    *
    * 
    * @param \OpenAPI\Client\Model\User $update_by
    */

    public function __construct(
        public int $id,
        public int $slot_id,
        public int $provider_id,
        public string $type = 'manual',
        public string $name,
        public int $count,
        public int $bet,
        public string $currency,
        public string $status = 'active',
        public bool $is_active = false,
        public int $author_id,
        public bool $in_process = false,
        public array $bounds,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public \OpenAPI\Client\Model\User $author,
        public \OpenAPI\Client\Model\User $update_by,
        public ?string $bonus_id = null,
        public ?string $bet_id = null,
        public ?string $denomination = null,
        public ?int $updated_by_author_id = null,
        public ?\DateTime $start_at = null,
        public ?\DateTime $expired_at = null,
        public ?string $data = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

