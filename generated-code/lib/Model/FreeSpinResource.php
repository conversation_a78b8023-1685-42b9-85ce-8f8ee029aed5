<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * FreeSpinResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $general_provider_id
    *
    * bigint
    * @param null | int $aggregator_id
    *
    * bigint
    * @param int $slot_id
    *
    * bigint
    * @param int $provider_id
    *
    * bigint
    * @param int $bonus_id
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $product_name
    *
    * integer
    * @param int $count
    *
    * integer
    * @param int $bet
    *
    * string(255)
    * @param string $bet_id
    *
    * string(3)
    * @param string $currency
    *
    * string(255)
    * @param string $status
    *
    * 
    * @param null | float $denomination
    *
    * boolean
    * @param bool $is_active
    *
    * bigint
    * @param int $author_id
    *
    * bigint
    * @param null | int $updated_by_author_id
    *
    * boolean
    * @param bool $in_process
    *
    * datetime
    * @param null | \DateTime $start_at
    *
    * datetime
    * @param null | \DateTime $expired_at
    *
    * 
    * @param object $data
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param array[] $users
    *
    * 
    * @param \OpenAPI\Client\Model\User $author
    *
    * 
    * @param \OpenAPI\Client\Model\User $update_by
    */

    public function __construct(
        public int $id,
        public int $general_provider_id,
        public int $slot_id,
        public int $provider_id,
        public int $bonus_id,
        public string $type = 'manual',
        public string $name,
        public string $product_name,
        public int $count,
        public int $bet,
        public string $bet_id = '0',
        public string $currency,
        public string $status = 'active',
        public bool $is_active = false,
        public int $author_id,
        public bool $in_process = false,
        public object $data,
        public array $users,
        public \OpenAPI\Client\Model\User $author,
        public \OpenAPI\Client\Model\User $update_by,
        public ?int $aggregator_id = null,
        public ?float $denomination = null,
        public ?int $updated_by_author_id = null,
        public ?\DateTime $start_at = null,
        public ?\DateTime $expired_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

