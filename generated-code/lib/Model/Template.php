<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * Template
        */
        namespace OpenAPI\Client\Model;

        /**
        * Template
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Template
{
    /**
    *
    * bigint
    * @param int $__version__
    *
    * string(255)
    * @param string $id
    *
    * text
    * @param null | string $name
    *
    * boolean
    * @param bool $is_active
    *
    * integer
    * @param int $max_bonus_number
    *
    * string(255)
    * @param string $type
    *
    * string(255)
    * @param string $operator_id
    *
    * string(255)
    * @param string $brand_id
    *
    * datetime
    * @param null | string $event_scheduled
    *
    * datetime
    * @param null | float $from_time
    *
    * datetime
    * @param null | float $to_time
    *
    * 
    * @param float $days_to_use
    *
    * text
    * @param null | string $events_availability
    *
    * text
    * @param null | string $restrictions
    *
    * 
    * @param \OpenAPI\Client\Model\TemplateFreebetData $freebet_data
    */

    public function __construct(
        public int $__version__,
        public string $id,
        public bool $is_active = false,
        public int $max_bonus_number,
        public string $type,
        public string $operator_id,
        public string $brand_id,
        public float $days_to_use,
        public \OpenAPI\Client\Model\TemplateFreebetData $freebet_data,
        public ?string $name = null,
        public ?string $event_scheduled = null,
        public ?float $from_time = null,
        public ?float $to_time = null,
        public ?string $events_availability = null,
        public ?string $restrictions = null,
    ) {}
}

