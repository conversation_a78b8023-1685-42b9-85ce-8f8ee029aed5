<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $weight
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $deposit_factors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $min_deposit
    *
    * float
    * @param null | float $min_factor
    *
    * bigint
    * @param null | int $min_bet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $total_transferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $total_uses
    *
    * boolean
    * @param bool $is_external
    *
    * boolean
    * @param bool $is_promo
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonus_data
    *
    * boolean
    * @param bool $is_welcome
    *
    * boolean
    * @param bool $is_onetime
    *
    * boolean
    * @param bool $is_no_dep
    *
    * boolean
    * @param bool $is_deposit
    *
    * boolean
    * @param bool $is_free_spin_for_deposit
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param int[] $slot_providers
    *
    * 
    * @param int[] $slots
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param null | int $max_real_balance
    *
    * 
    * @param null | int $genre_id
    */

    public function __construct(
        public int $id,
        public int $weight,
        public string $name,
        public object $bonus_name,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public array $max_bonuses,
        public array $max_transfers,
        public array $deposit_factors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $total_transferred,
        public int $uses,
        public int $transfers,
        public int $total_uses,
        public bool $is_external = false,
        public bool $is_promo = false,
        public object $bonus_data,
        public bool $is_welcome,
        public bool $is_onetime,
        public bool $is_no_dep,
        public bool $is_deposit,
        public bool $is_free_spin_for_deposit,
        public array $slot_providers,
        public array $slots,
        public \OpenAPI\Client\Model\FreeSpin $freespin,
        public array $trigger_sessions,
        public ?string $image = null,
        public ?int $min_deposit = null,
        public ?float $min_factor = null,
        public ?int $min_bet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?int $max_real_balance = null,
        public ?int $genre_id = null,
    ) {}
}

