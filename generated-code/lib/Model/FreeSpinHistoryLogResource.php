<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * FreeSpinHistoryLogResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinHistoryLogResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinHistoryLogResource
{
    /**
    *
    * 
    * @param int $id
    *
    * 
    * @param int $free_spin_id
    *
    * 
    * @param string $type
    *
    * 
    * @param string $author_email
    *
    * 
    * @param string[] $data
    *
    * datetime
    * @param \DateTime $created_at
    */

    public function __construct(
        public int $id,
        public int $free_spin_id,
        public string $type,
        public string $author_email,
        public array $data,
        public \DateTime $created_at,
    ) {}
}

