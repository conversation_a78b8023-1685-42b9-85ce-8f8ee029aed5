<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * Player
        */
        namespace OpenAPI\Client\Model;

        /**
        * Player
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Player
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param null | string $username
    *
    * string(255)
    * @param string $password
    *
    * string(255)
    * @param null | string $email
    *
    * boolean
    * @param bool $is_under_moderation
    *
    * boolean
    * @param bool $payout_without_moderation
    *
    * boolean
    * @param bool $subscribed
    *
    * integer
    * @param null | int $bonus_balance_id
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $email_verified_at
    *
    * string(255)
    * @param null | string $phone
    *
    * string(6)
    * @param null | string $phone_code
    *
    * datetime
    * @param null | \DateTime $phone_verified_at
    *
    * string(255)
    * @param string $first_name
    *
    * string(255)
    * @param null | string $last_name
    *
    * date
    * @param null | \DateTime $birth
    *
    * string(2)
    * @param null | string $country
    *
    * string(255)
    * @param null | string $city
    *
    * string(7)
    * @param null | string $gender
    *
    * string(2)
    * @param null | string $language
    *
    * string(255)
    * @param null | string $click_id
    *
    * string(3)
    * @param string $currency
    *
    * string(45)
    * @param null | string $last_seen_ip
    *
    * boolean
    * @param bool $blocked
    *
    * text(65535)
    * @param null | string $comment
    *
    * integer
    * @param null | int $welcome_bonus_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $password,
        public bool $is_under_moderation = false,
        public bool $payout_without_moderation = false,
        public bool $subscribed = false,
        public string $uuid,
        public string $first_name,
        public string $currency,
        public bool $blocked = false,
        public ?string $username = null,
        public ?string $email = null,
        public ?int $bonus_balance_id = null,
        public ?\DateTime $email_verified_at = null,
        public ?string $phone = null,
        public ?string $phone_code = null,
        public ?\DateTime $phone_verified_at = null,
        public ?string $last_name = null,
        public ?\DateTime $birth = null,
        public ?string $country = null,
        public ?string $city = null,
        public ?string $gender = null,
        public ?string $language = null,
        public ?string $click_id = null,
        public ?string $last_seen_ip = null,
        public ?string $comment = null,
        public ?int $welcome_bonus_id = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

