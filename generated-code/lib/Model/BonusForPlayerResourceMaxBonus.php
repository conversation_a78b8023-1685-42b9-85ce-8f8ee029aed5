<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusForPlayerResourceMaxBonus
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusForPlayerResourceMaxBonus
        */
            use <PERSON>rell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResourceMaxBonus
{
    /**
    *
    * 
    * @param string $amount
    *
    * string(3)
    * @param string $currency
    */

    public function __construct(
        public string $amount,
        public string $currency,
    ) {}
}

