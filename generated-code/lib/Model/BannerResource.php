<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BannerResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BannerResource
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BannerResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * string(2)
    * @param null | string $language
    *
    * string(3)
    * @param null | string $country
    *
    * string(255)
    * @param string $is_for_signed_in
    *
    * string(255)
    * @param string $platform
    *
    * boolean
    * @param bool $mobile_apk_install
    *
    * string(255)
    * @param string $location
    *
    * bigint
    * @param int $weight
    *
    * string(255)
    * @param string $type
    *
    * 
    * @param object $disposition
    *
    * string(255)
    * @param string $image
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $is_active
    *
    * integer
    * @param null | int $smartico_segment_id
    *
    * integer
    * @param null | int $start_at
    *
    * integer
    * @param null | int $end_at
    *
    * string(255)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public string $name,
        public string $is_for_signed_in,
        public string $platform,
        public bool $mobile_apk_install,
        public string $location,
        public int $weight = 0,
        public string $type,
        public object $disposition,
        public string $image,
        public bool $enabled,
        public bool $is_active,
        public ?string $language = null,
        public ?string $country = null,
        public ?int $smartico_segment_id = null,
        public ?int $start_at = null,
        public ?int $end_at = null,
        public ?string $uuid = null,
    ) {}
}

