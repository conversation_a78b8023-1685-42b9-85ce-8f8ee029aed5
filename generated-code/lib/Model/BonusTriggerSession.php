<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusTriggerSession
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusTriggerSession
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusTriggerSession
{
    /**
    *
    * datetime
    * @param \DateTime $start_at
    *
    * datetime
    * @param \DateTime $end_at
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public \DateTime $start_at,
        public \DateTime $end_at,
        public ?string $uuid = null,
    ) {}
}

