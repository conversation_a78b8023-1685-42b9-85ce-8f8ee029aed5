<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusForPlayerResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusForPlayerResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusForPlayerResource
{
    /**
    *
    * 
    * @param int $id
    *
    * string(255)
    * @param string $name
    *
    * 
    * @param object $bonus_name
    *
    * 
    * @param object $description
    *
    * 
    * @param object $condition
    *
    * 
    * @param string $type
    *
    * 
    * @param null | string $image
    *
    * boolean
    * @param bool $crash
    *
    * 
    * @param \OpenAPI\Client\Model\BonusForPlayerResourceMaxBonus $max_bonus
    *
    * 
    * @param int[] $max_bonuses
    *
    * 
    * @param int[] $max_transfers
    *
    * 
    * @param int[] $deposit_factors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $min_deposit
    *
    * bigint
    * @param null | int $min_bet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * datetime
    * @param null | \DateTime $from
    *
    * datetime
    * @param null | \DateTime $to
    *
    * bigint
    * @param null | int $duration
    *
    * 
    * @param object $bonus_data
    *
    * boolean
    * @param bool $is_welcome
    *
    * boolean
    * @param bool $is_onetime
    *
    * boolean
    * @param bool $is_no_dep
    *
    * boolean
    * @param bool $is_deposit
    *
    * boolean
    * @param bool $is_free_spin_for_deposit
    *
    * 
    * @param int $weight
    *
    * boolean
    * @param bool $is_organic
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    */

    public function __construct(
        public int $id,
        public string $name,
        public object $bonus_name,
        public object $description,
        public object $condition,
        public string $type = 'wager',
        public bool $crash = false,
        public \OpenAPI\Client\Model\BonusForPlayerResourceMaxBonus $max_bonus,
        public array $max_bonuses,
        public array $max_transfers,
        public array $deposit_factors,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public object $bonus_data,
        public bool $is_welcome,
        public bool $is_onetime,
        public bool $is_no_dep,
        public bool $is_deposit,
        public bool $is_free_spin_for_deposit,
        public int $weight,
        public bool $is_organic,
        public array $trigger_sessions,
        public ?string $image = null,
        public ?int $min_deposit = null,
        public ?int $min_bet = null,
        public ?\DateTime $from = null,
        public ?\DateTime $to = null,
        public ?int $duration = null,
    ) {}
}

