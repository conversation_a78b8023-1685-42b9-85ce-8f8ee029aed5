<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * UserBalance
        */
        namespace OpenAPI\Client\Model;

        /**
        * UserBalance
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class UserBalance
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $player_id
    *
    * string(3)
    * @param string $currency
    *
    * integer
    * @param null | int $last_transaction_id
    *
    * bigint
    * @param int $balance
    *
    * bigint
    * @param int $debt
    *
    * bigint
    * @param int $deposit
    *
    * bigint
    * @param int $wager
    *
    * bigint
    * @param int $withdraw
    *
    * bigint
    * @param int $in_game
    *
    * bigint
    * @param int $current_payout_limit
    *
    * bigint
    * @param int $total_bet
    *
    * bigint
    * @param int $total_profit
    *
    * bigint
    * @param int $payout_approved
    *
    * bigint
    * @param int $payout_wait
    */

    public function __construct(
        public int $id,
        public int $player_id,
        public string $currency,
        public int $balance,
        public int $debt,
        public int $deposit,
        public int $wager,
        public int $withdraw,
        public int $in_game,
        public int $current_payout_limit,
        public int $total_bet,
        public int $total_profit,
        public int $payout_approved,
        public int $payout_wait,
        public ?int $last_transaction_id = null,
    ) {}
}

