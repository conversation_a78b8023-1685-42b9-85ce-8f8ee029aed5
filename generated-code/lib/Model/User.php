<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * User
        */
        namespace OpenAPI\Client\Model;

        /**
        * User
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class User
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $client_id
    *
    * string(255)
    * @param string $login
    *
    * string(255)
    * @param string $email
    *
    * integer
    * @param null | int $role_id
    *
    * string(45)
    * @param null | string $last_seen_ip
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * datetime
    * @param null | \DateTime $deleted_at
    */

    public function __construct(
        public int $id,
        public int $client_id = 2,
        public string $login,
        public string $email,
        public ?int $role_id = null,
        public ?string $last_seen_ip = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?\DateTime $deleted_at = null,
    ) {}
}

