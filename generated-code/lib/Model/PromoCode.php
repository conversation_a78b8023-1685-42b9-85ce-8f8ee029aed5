<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * PromoCode
        */
        namespace OpenAPI\Client\Model;

        /**
        * PromoCode
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCode
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $stream_id
    *
    * bigint
    * @param int $bonus_id
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $is_active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $use_limit
    *
    * datetime
    * @param null | int $start_at
    *
    * datetime
    * @param null | int $end_at
    *
    * string
    * @param object $condition
    *
    * boolean
    * @param bool $is_alanbase
    *
    * string(36)
    * @param string $uuid
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $code,
        public int $bonus_id,
        public bool $is_active = false,
        public int $uses,
        public int $use_limit,
        public object $condition,
        public bool $is_alanbase = false,
        public string $uuid,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?int $stream_id = null,
        public ?string $description = null,
        public ?int $start_at = null,
        public ?int $end_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

