<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * Slot
        */
        namespace OpenAPI\Client\Model;

        /**
        * Slot
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Slot
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $provider
    *
    * integer
    * @param int $version
    *
    * bigint
    * @param int $external_provider_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $slug
    *
    * string(255)
    * @param string $internal_id
    *
    * string(255)
    * @param string $external_id
    *
    * boolean
    * @param bool $enabled
    *
    * boolean
    * @param bool $suspended
    *
    * text
    * @param string $meta
    *
    * string(255)
    * @param string $image
    *
    * text(16777215)
    * @param null | string $description
    *
    * boolean
    * @param bool $is_mobile
    *
    * boolean
    * @param bool $is_bonus_ready
    *
    * boolean
    * @param bool $is_wager_ready
    *
    * boolean
    * @param bool $is_desktop
    *
    * boolean
    * @param bool $has_lobby
    *
    * boolean
    * @param bool $has_freespins
    *
    * boolean
    * @param null | bool $is_demo
    *
    * integer
    * @param int $genre_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * datetime
    * @param null | \DateTime $deleted_at
    *
    * string(255)
    * @param string $friendly_url
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $provider,
        public int $version,
        public int $external_provider_id,
        public string $name,
        public string $slug,
        public string $internal_id,
        public string $external_id,
        public bool $enabled,
        public bool $suspended,
        public string $meta,
        public string $image,
        public bool $is_mobile,
        public bool $is_bonus_ready = false,
        public bool $is_wager_ready = false,
        public bool $is_desktop,
        public bool $has_lobby,
        public bool $has_freespins = false,
        public int $genre_id,
        public string $friendly_url,
        public ?string $description = null,
        public ?bool $is_demo = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?\DateTime $deleted_at = null,
        public ?string $uuid = null,
    ) {}
}

