<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * SlotsProvider
        */
        namespace OpenAPI\Client\Model;

        /**
        * SlotsProvider
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class SlotsProvider
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * bigint
    * @param int $subscription_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param null | string $custom_name
    *
    * string(255)
    * @param null | string $image
    *
    * boolean
    * @param bool $suspended
    *
    * boolean
    * @param bool $is_bonus_barred
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\Slot[] $get_providers
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public int $subscription_id,
        public string $name,
        public bool $suspended = false,
        public bool $is_bonus_barred = false,
        public array $get_providers,
        public ?string $custom_name = null,
        public ?string $image = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

