<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * DataSubscriptions
        */
        namespace OpenAPI\Client\Model;

        /**
        * DataSubscriptions
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class DataSubscriptions
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $sub_id
    *
    * string(255)
    * @param string $access_token
    *
    * string(255)
    * @param null | string $game_token
    *
    * string(255)
    * @param string $provider
    *
    * string(255)
    * @param string $provider_company
    *
    * boolean
    * @param bool $is_approved
    *
    * boolean
    * @param bool $is_slot
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $sub_id,
        public string $access_token,
        public string $provider,
        public string $provider_company,
        public bool $is_approved = false,
        public bool $is_slot = false,
        public ?string $game_token = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

