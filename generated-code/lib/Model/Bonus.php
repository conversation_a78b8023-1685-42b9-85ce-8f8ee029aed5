<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * Bonus
        */
        namespace OpenAPI\Client\Model;

        /**
        * Bonus
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class Bonus
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * json
    * @param null | string $bonus_name
    *
    * text
    * @param null | string $description
    *
    * json
    * @param null | string $condition
    *
    * string(40)
    * @param string $type
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param string $bonuses
    *
    * json
    * @param string $max_transfers
    *
    * json
    * @param null | string $deposit_factors
    *
    * string(3)
    * @param string $currency
    *
    * decimal
    * @param float $wager
    *
    * integer
    * @param null | int $min_deposit
    *
    * float
    * @param null | float $min_factor
    *
    * bigint
    * @param null | int $min_bet
    *
    * boolean
    * @param bool $active
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param bool $bets
    *
    * boolean
    * @param bool $crash
    *
    * bigint
    * @param int $total_transferred
    *
    * integer
    * @param int $uses
    *
    * bigint
    * @param int $transfers
    *
    * bigint
    * @param int $total_uses
    *
    * boolean
    * @param bool $is_external
    *
    * boolean
    * @param bool $is_promo
    *
    * datetime
    * @param null | \DateTime $active_from
    *
    * datetime
    * @param null | \DateTime $active_til
    *
    * bigint
    * @param null | int $duration
    *
    * text
    * @param string $data
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param int[] $slot_providers
    *
    * 
    * @param \OpenAPI\Client\Model\FreeSpin $freespin
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $trigger_sessions
    *
    * 
    * @param \OpenAPI\Client\Model\BonusTriggerSession[] $active_trigger_sessions
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $type = 'wager',
        public string $bonuses,
        public string $max_transfers,
        public string $currency,
        public float $wager,
        public bool $active,
        public bool $casino,
        public bool $bets,
        public bool $crash = false,
        public int $total_transferred,
        public int $uses,
        public int $transfers,
        public int $total_uses,
        public bool $is_external = false,
        public bool $is_promo = false,
        public string $data,
        public array $slot_providers,
        public \OpenAPI\Client\Model\FreeSpin $freespin,
        public array $trigger_sessions,
        public array $active_trigger_sessions,
        public ?string $bonus_name = null,
        public ?string $description = null,
        public ?string $condition = null,
        public ?string $image = null,
        public ?string $deposit_factors = null,
        public ?int $min_deposit = null,
        public ?float $min_factor = null,
        public ?int $min_bet = null,
        public ?\DateTime $active_from = null,
        public ?\DateTime $active_til = null,
        public ?int $duration = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

