<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * PromoCodeResource
        */
        namespace OpenAPI\Client\Model;

        /**
        * PromoCodeResource
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class PromoCodeResource
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * string(255)
    * @param string $code
    *
    * bigint
    * @param null | int $stream_id
    *
    * bigint
    * @param int $bonus_id
    *
    * text
    * @param null | string $description
    *
    * boolean
    * @param bool $active
    *
    * integer
    * @param int $uses
    *
    * integer
    * @param int $limit
    *
    * datetime
    * @param null | int $start_at
    *
    * datetime
    * @param null | int $end_at
    *
    * 
    * @param object $condition
    *
    * boolean
    * @param bool $is_alanbase
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\BonusResource $bonus
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $code,
        public int $bonus_id,
        public bool $active = false,
        public int $uses,
        public int $limit,
        public object $condition,
        public bool $is_alanbase = false,
        public string $uuid,
        public \OpenAPI\Client\Model\BonusResource $bonus,
        public ?int $stream_id = null,
        public ?string $description = null,
        public ?int $start_at = null,
        public ?int $end_at = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

