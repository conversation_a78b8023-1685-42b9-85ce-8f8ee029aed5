<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusInfo
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusInfo
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusInfo
{
    /**
    *
    * bigint
    * @param int $id
    *
    * integer
    * @param int $client_id
    *
    * string(255)
    * @param string $name
    *
    * string(3)
    * @param string $currency
    *
    * boolean
    * @param bool $active
    *
    * datetime
    * @param null | \DateTime $visible_from
    *
    * datetime
    * @param null | \DateTime $visible_to
    *
    * boolean
    * @param bool $casino
    *
    * boolean
    * @param null | bool $is_welcome
    *
    * boolean
    * @param bool $is_for_smartico
    *
    * string(255)
    * @param null | string $image
    *
    * json
    * @param null | string $bonus_name
    *
    * json
    * @param null | string $description
    *
    * json
    * @param null | string $description_info
    *
    * json
    * @param null | string $condition_title
    *
    * json
    * @param null | string $condition
    *
    * json
    * @param null | string $colors
    *
    * integer
    * @param int $sort_order
    *
    * string(255)
    * @param null | string $proceed_link
    *
    * bigint
    * @param null | int $bonus_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * string(36)
    * @param null | string $uuid
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $client_id,
        public string $name,
        public string $currency,
        public bool $active,
        public bool $casino,
        public bool $is_for_smartico = false,
        public int $sort_order,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?\DateTime $visible_from = null,
        public ?\DateTime $visible_to = null,
        public ?bool $is_welcome = false,
        public ?string $image = null,
        public ?string $bonus_name = null,
        public ?string $description = null,
        public ?string $description_info = null,
        public ?string $condition_title = null,
        public ?string $condition = null,
        public ?string $colors = null,
        public ?string $proceed_link = null,
        public ?int $bonus_id = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
        public ?string $uuid = null,
    ) {}
}

