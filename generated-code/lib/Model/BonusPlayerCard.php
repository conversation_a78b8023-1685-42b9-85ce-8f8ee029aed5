<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * BonusPlayerCard
        */
        namespace OpenAPI\Client\Model;

        /**
        * BonusPlayerCard
        */
            use Crell\Serde\Renaming\Cases;
use Crell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class BonusPlayerCard
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $player_id
    *
    * boolean
    * @param bool $is_used
    *
    * integer
    * @param null | int $bonus_id
    *
    * integer
    * @param null | int $bonus_info_id
    *
    * datetime
    * @param null | \DateTime $created_at
    *
    * datetime
    * @param null | \DateTime $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\User $player
    *
    * 
    * @param \OpenAPI\Client\Model\Bonus $bonus
    */

    public function __construct(
        public int $id,
        public int $player_id,
        public bool $is_used = false,
        public \OpenAPI\Client\Model\User $player,
        public \OpenAPI\Client\Model\Bonus $bonus,
        public ?int $bonus_id = null,
        public ?int $bonus_info_id = null,
        public ?\DateTime $created_at = null,
        public ?\DateTime $updated_at = null,
    ) {}
}

