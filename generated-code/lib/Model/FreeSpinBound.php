<?php declare(strict_types=1);

/**
 * <PERSON><PERSON>
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 *
 * Source files are located at:
 *
 * > https://github.com/OpenAPITools/openapi-generator/blob/master/modules/openapi-generator/src/main/resources/php-laravel/
 */


        /**
        * FreeSpinBound
        */
        namespace OpenAPI\Client\Model;

        /**
        * FreeSpinBound
        */
            use Crell\Serde\Renaming\Cases;
use <PERSON>rell\Serde\Attributes as Serde;

#[Serde\ClassSettings(renameWith: Cases::snake_case)]
class FreeSpinBound
{
    /**
    *
    * bigint
    * @param int $id
    *
    * bigint
    * @param int $free_spin_id
    *
    * string(32)
    * @param string $uuid
    *
    * bigint
    * @param null | int $bonus_id
    *
    * bigint
    * @param int $bet
    *
    * string(255)
    * @param string $bet_id
    *
    * integer
    * @param int $total_win
    *
    * integer
    * @param int $count_left
    *
    * boolean
    * @param bool $is_active
    *
    * boolean
    * @param bool $is_archived
    *
    * text(65535)
    * @param null | string $message
    *
    * datetime
    * @param null | float $start_at
    *
    * datetime
    * @param null | float $expire_at
    *
    * datetime
    * @param null | float $canceled_at
    *
    * boolean
    * @param bool $is_user_notified
    *
    * datetime
    * @param null | float $created_at
    *
    * datetime
    * @param null | float $updated_at
    *
    * 
    * @param \OpenAPI\Client\Model\Player $player_info
    */

    public function __construct(
        public int $id,
        public int $free_spin_id,
        public string $uuid,
        public int $bet,
        public string $bet_id,
        public int $total_win,
        public int $count_left,
        public bool $is_active = true,
        public bool $is_archived = false,
        public bool $is_user_notified = false,
        public \OpenAPI\Client\Model\Player $player_info,
        public ?int $bonus_id = null,
        public ?string $message = null,
        public ?float $start_at = null,
        public ?float $expire_at = null,
        public ?float $canceled_at = null,
        public ?float $created_at = null,
        public ?float $updated_at = null,
    ) {}
}

