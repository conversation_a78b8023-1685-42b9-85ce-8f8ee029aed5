# OpenAPI\Client\BonusesApi

All URIs are relative to http://bonus-management.whitelabel.dd/api, except if the operation defines another base path.

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**createBonus()**](BonusesApi.md#createBonus) | **POST** /api/bonuses/add |  |
| [**deleteBonus()**](BonusesApi.md#deleteBonus) | **DELETE** /api/bonuses/{id} |  |
| [**updateBonus()**](BonusesApi.md#updateBonus) | **PUT** /api/bonuses/{id} |  |


## `createBonus()`

```php
createBonus($x_signature, $x_cluster_connection, $create_bonus_request): \OpenAPI\Client\Model\BonusResource
```



create bonus

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');



$apiInstance = new OpenAPI\Client\Api\BonusesApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client()
);
$x_signature = 'x_signature_example'; // string | 
$x_cluster_connection = 'x_cluster_connection_example'; // string | 
$create_bonus_request = new \OpenAPI\Client\Model\CreateBonusRequest(); // \OpenAPI\Client\Model\CreateBonusRequest

try {
    $result = $apiInstance->createBonus($x_signature, $x_cluster_connection, $create_bonus_request);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling BonusesApi->createBonus: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **x_signature** | **string**|  | [optional] |
| **x_cluster_connection** | **string**|  | [optional] |
| **create_bonus_request** | [**\OpenAPI\Client\Model\CreateBonusRequest**](../Model/CreateBonusRequest.md)|  | [optional] |

### Return type

[**\OpenAPI\Client\Model\BonusResource**](../Model/BonusResource.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: `application/json`
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `deleteBonus()`

```php
deleteBonus($id, $x_signature, $x_cluster_connection)
```



delete bonus

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');



$apiInstance = new OpenAPI\Client\Api\BonusesApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client()
);
$id = 'id_example'; // string | 
$x_signature = 'x_signature_example'; // string | 
$x_cluster_connection = 'x_cluster_connection_example'; // string | 

try {
    $apiInstance->deleteBonus($id, $x_signature, $x_cluster_connection);
} catch (Exception $e) {
    echo 'Exception when calling BonusesApi->deleteBonus: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **id** | **string**|  | |
| **x_signature** | **string**|  | [optional] |
| **x_cluster_connection** | **string**|  | [optional] |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `updateBonus()`

```php
updateBonus($id, $x_signature, $x_cluster_connection, $update_bonus_request): \OpenAPI\Client\Model\BonusResource
```



update bonus

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');



$apiInstance = new OpenAPI\Client\Api\BonusesApi(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client()
);
$id = 'id_example'; // string | 
$x_signature = 'x_signature_example'; // string | 
$x_cluster_connection = 'x_cluster_connection_example'; // string | 
$update_bonus_request = new \OpenAPI\Client\Model\UpdateBonusRequest(); // \OpenAPI\Client\Model\UpdateBonusRequest

try {
    $result = $apiInstance->updateBonus($id, $x_signature, $x_cluster_connection, $update_bonus_request);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling BonusesApi->updateBonus: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **id** | **string**|  | |
| **x_signature** | **string**|  | [optional] |
| **x_cluster_connection** | **string**|  | [optional] |
| **update_bonus_request** | [**\OpenAPI\Client\Model\UpdateBonusRequest**](../Model/UpdateBonusRequest.md)|  | [optional] |

### Return type

[**\OpenAPI\Client\Model\BonusResource**](../Model/BonusResource.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: `application/json`
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)
