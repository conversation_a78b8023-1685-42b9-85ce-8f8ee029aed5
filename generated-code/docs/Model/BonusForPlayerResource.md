# # BonusForPlayerResource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** |  | [optional]
**name** | **string** | string(255) | [optional]
**bonus_name** | **object** |  | [optional]
**description** | **object** |  | [optional]
**condition** | **object** |  | [optional]
**type** | **string** |  | [optional] [default to 'wager']
**image** | **string** |  | [optional]
**crash** | **bool** | boolean | [optional] [default to false]
**max_bonus** | [**\OpenAPI\Client\Model\BonusForPlayerResourceMaxBonus**](BonusForPlayerResourceMaxBonus.md) |  | [optional]
**max_bonuses** | **int[]** |  | [optional]
**max_transfers** | **int[]** |  | [optional]
**deposit_factors** | **int[]** |  | [optional]
**currency** | **string** | string(3) | [optional]
**wager** | **float** | decimal | [optional]
**min_deposit** | **int** | integer | [optional]
**min_bet** | **int** | bigint | [optional]
**active** | **bool** | boolean | [optional]
**casino** | **bool** | boolean | [optional]
**bets** | **bool** | boolean | [optional]
**from** | **\DateTime** | datetime | [optional]
**to** | **\DateTime** | datetime | [optional]
**duration** | **int** | bigint | [optional]
**bonus_data** | **object** |  | [optional]
**is_welcome** | **bool** | boolean | [optional]
**is_onetime** | **bool** | boolean | [optional]
**is_no_dep** | **bool** | boolean | [optional]
**is_deposit** | **bool** | boolean | [optional]
**is_free_spin_for_deposit** | **bool** | boolean | [optional]
**weight** | **int** |  | [optional]
**is_organic** | **bool** | boolean | [optional]
**trigger_sessions** | [**\OpenAPI\Client\Model\BonusTriggerSession[]**](BonusTriggerSession.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
