# # Player

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**client_id** | **int** | bigint |
**username** | **string** | string(255) | [optional]
**password** | **string** | string(255) |
**email** | **string** | string(255) | [optional]
**is_under_moderation** | **bool** | boolean | [default to false]
**payout_without_moderation** | **bool** | boolean | [default to false]
**subscribed** | **bool** | boolean | [default to false]
**bonus_balance_id** | **int** | integer | [optional]
**uuid** | **string** | string(36) |
**email_verified_at** | **\DateTime** | datetime | [optional]
**phone** | **string** | string(255) | [optional]
**phone_code** | **string** | string(6) | [optional]
**phone_verified_at** | **\DateTime** | datetime | [optional]
**first_name** | **string** | string(255) |
**last_name** | **string** | string(255) | [optional]
**birth** | **\DateTime** | date | [optional]
**country** | **string** | string(2) | [optional]
**city** | **string** | string(255) | [optional]
**gender** | **string** | string(7) | [optional]
**language** | **string** | string(2) | [optional]
**click_id** | **string** | string(255) | [optional]
**currency** | **string** | string(3) |
**last_seen_ip** | **string** | string(45) | [optional]
**blocked** | **bool** | boolean | [default to false]
**comment** | **string** | text(65535) | [optional]
**welcome_bonus_id** | **int** | integer | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
