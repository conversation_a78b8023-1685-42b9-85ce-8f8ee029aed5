# # BonusBalance

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**log_key** | **string** | string(255) | [optional]
**bonus_id** | **int** | bigint |
**type** | **string** | string(40) | [default to 'wager']
**player_id** | **int** | bigint |
**balance** | **int** | bigint |
**bonus_external_id** | **string** | string(255) | [optional]
**status** | **string** | string(40) |
**orig_bonus** | **int** | bigint |
**orig_wager** | **int** | bigint |
**wager** | **int** | bigint |
**min_factor** | **float** | float | [optional]
**min_bet** | **int** | bigint | [optional]
**expire_at** | **\DateTime** | datetime | [optional]
**in_game** | **int** | bigint |
**transfer** | **int** | bigint |
**currency** | **string** | string(3) |
**active** | **bool** | boolean |
**popup_seen** | **bool** | boolean | [default to false]
**casino** | **bool** | boolean |
**bets** | **bool** | boolean |
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**player** | [**\OpenAPI\Client\Model\User**](User.md) |  | [optional]
**bonus** | [**\OpenAPI\Client\Model\Bonus**](Bonus.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
