# # FreeSpinBound

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**free_spin_id** | **int** | bigint |
**uuid** | **string** | string(32) |
**bonus_id** | **int** | bigint | [optional]
**bet** | **int** | bigint |
**bet_id** | **string** | string(255) |
**total_win** | **int** | integer |
**count_left** | **int** | integer |
**is_active** | **bool** | boolean | [default to true]
**is_archived** | **bool** | boolean | [default to false]
**message** | **string** | text(65535) | [optional]
**start_at** | **float** | datetime | [optional]
**expire_at** | **float** | datetime | [optional]
**canceled_at** | **float** | datetime | [optional]
**is_user_notified** | **bool** | boolean | [default to false]
**created_at** | **float** | datetime | [optional]
**updated_at** | **float** | datetime | [optional]
**player_info** | [**\OpenAPI\Client\Model\Player**](Player.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
