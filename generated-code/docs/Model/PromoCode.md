# # PromoCode

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**client_id** | **int** | bigint | [optional]
**name** | **string** | string(255) |
**code** | **string** | string(255) |
**stream_id** | **int** | bigint | [optional]
**bonus_id** | **int** | bigint |
**description** | **string** | text | [optional]
**is_active** | **bool** | boolean | [optional] [default to false]
**uses** | **int** | integer | [optional]
**use_limit** | **int** | integer | [optional]
**start_at** | **int** | datetime | [optional]
**end_at** | **int** | datetime | [optional]
**condition** | **object** | string |
**is_alanbase** | **bool** | boolean | [optional] [default to false]
**uuid** | **string** | string(36) | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**bonus** | [**\OpenAPI\Client\Model\Bonus**](Bonus.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
