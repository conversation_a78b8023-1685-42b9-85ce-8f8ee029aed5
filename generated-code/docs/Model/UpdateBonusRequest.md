# # UpdateBonusRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  |
**image** | **string** |  | [optional]
**description** | **object** |  | [optional]
**condition** | **object** |  | [optional]
**bonus_name** | **object** |  | [optional]
**type** | **string** |  |
**min_bet** | **int** |  | [optional]
**min_deposit** | **int** |  | [optional]
**max_transfers** | **int[]** |  | [optional]
**max_bonuses** | **int[]** |  | [optional]
**deposit_factors** | **int[]** |  | [optional]
**duration** | **int** |  | [optional]
**wager** | **float** |  | [optional]
**currency** | **string** |  | [optional]
**active** | **bool** |  | [optional]
**casino** | **bool** |  | [optional]
**bets** | **bool** |  | [optional]
**from** | **string** |  | [optional]
**to** | **string** |  | [optional]
**min_factor** | **float** |  | [optional]
**data** | **object** |  | [optional]
**providers_ids** | **int[]** |  | [optional]
**slots_ids** | **int[]** |  | [optional]
**trigger_sessions** | [**\OpenAPI\Client\Model\BonusTriggerSession[]**](BonusTriggerSession.md) | required if type is free_spins_for_deposit | [optional]
**segment_id** | **int** |  | [optional]
**is_promo** | **bool** |  | [optional]
**is_external** | **bool** |  | [optional]
**author_id** | **int** | required if type is free_spins_for_deposit | [optional]
**slots_id** | **int** | required if type is free_spins_for_deposit | [optional]
**provider_id** | **int** | required if type is free_spins_for_deposit | [optional]
**bonus_balance** | **bool** | required if type is free_spins_for_deposit | [optional]
**count** | **int** | required if type is free_spins_for_deposit | [optional]
**bet** | **float** | required if type is free_spins_for_deposit | [optional]
**bet_id** | **string** | required if type is free_spins_for_deposit | [optional]
**denomination** | **float** | required if type is free_spins_for_deposit | [optional]
**max_real_balance** | **int** |  | [optional]
**genre_id** | **int** |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
