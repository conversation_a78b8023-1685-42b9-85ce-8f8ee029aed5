# # BonusInfo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**client_id** | **int** | integer |
**name** | **string** | string(255) |
**currency** | **string** | string(3) |
**active** | **bool** | boolean |
**visible_from** | **\DateTime** | datetime | [optional]
**visible_to** | **\DateTime** | datetime | [optional]
**casino** | **bool** | boolean |
**is_welcome** | **bool** | boolean | [optional] [default to false]
**is_for_smartico** | **bool** | boolean | [default to false]
**image** | **string** | string(255) | [optional]
**bonus_name** | **string** | json | [optional]
**description** | **string** | json | [optional]
**description_info** | **string** | json | [optional]
**condition_title** | **string** | json | [optional]
**condition** | **string** | json | [optional]
**colors** | **string** | json | [optional]
**sort_order** | **int** | integer |
**proceed_link** | **string** | string(255) | [optional]
**bonus_id** | **int** | bigint | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**uuid** | **string** | string(36) | [optional]
**bonus** | [**\OpenAPI\Client\Model\Bonus**](Bonus.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
