# # Bonus

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**client_id** | **int** | bigint |
**name** | **string** | string(255) |
**bonus_name** | **string** | json | [optional]
**description** | **string** | text | [optional]
**condition** | **string** | json | [optional]
**type** | **string** | string(40) | [default to 'wager']
**image** | **string** | string(255) | [optional]
**bonuses** | **string** | json |
**max_transfers** | **string** | json |
**deposit_factors** | **string** | json | [optional]
**currency** | **string** | string(3) |
**wager** | **float** | decimal |
**min_deposit** | **int** | integer | [optional]
**min_factor** | **float** | float | [optional]
**min_bet** | **int** | bigint | [optional]
**active** | **bool** | boolean |
**casino** | **bool** | boolean |
**bets** | **bool** | boolean |
**crash** | **bool** | boolean | [default to false]
**total_transferred** | **int** | bigint |
**uses** | **int** | integer |
**transfers** | **int** | bigint |
**total_uses** | **int** | bigint |
**is_external** | **bool** | boolean | [default to false]
**is_promo** | **bool** | boolean | [default to false]
**active_from** | **\DateTime** | datetime | [optional]
**active_til** | **\DateTime** | datetime | [optional]
**duration** | **int** | bigint | [optional]
**data** | **string** | text |
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**uuid** | **string** | string(36) | [optional]
**slot_providers** | **int[]** |  | [optional]
**freespin** | [**\OpenAPI\Client\Model\FreeSpin**](FreeSpin.md) |  | [optional]
**trigger_sessions** | [**\OpenAPI\Client\Model\BonusTriggerSession[]**](BonusTriggerSession.md) |  | [optional]
**active_trigger_sessions** | [**\OpenAPI\Client\Model\BonusTriggerSession[]**](BonusTriggerSession.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
