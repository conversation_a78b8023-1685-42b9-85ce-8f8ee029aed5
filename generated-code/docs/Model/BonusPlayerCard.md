# # BonusPlayerCard

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**player_id** | **int** | bigint |
**is_used** | **bool** | boolean | [default to false]
**bonus_id** | **int** | integer | [optional]
**bonus_info_id** | **int** | integer | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**player** | [**\OpenAPI\Client\Model\User**](User.md) |  | [optional]
**bonus** | [**\OpenAPI\Client\Model\Bonus**](Bonus.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
