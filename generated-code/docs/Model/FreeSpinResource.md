# # FreeSpinResource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**general_provider_id** | **int** | bigint |
**aggregator_id** | **int** | bigint | [optional]
**slot_id** | **int** | bigint |
**provider_id** | **int** | bigint |
**bonus_id** | **int** | bigint | [optional]
**type** | **string** | string(255) | [default to 'manual']
**name** | **string** | string(255) |
**product_name** | **string** | string(255) |
**count** | **int** | integer |
**bet** | **int** | integer |
**bet_id** | **string** | string(255) | [optional] [default to '0']
**currency** | **string** | string(3) |
**status** | **string** | string(255) | [default to 'active']
**denomination** | **float** |  | [optional]
**is_active** | **bool** | boolean | [default to false]
**author_id** | **int** | bigint |
**updated_by_author_id** | **int** | bigint | [optional]
**in_process** | **bool** | boolean | [default to false]
**start_at** | **\DateTime** | datetime | [optional]
**expired_at** | **\DateTime** | datetime | [optional]
**data** | **object** |  | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**users** | **array[]** |  | [optional]
**author** | [**\OpenAPI\Client\Model\User**](User.md) |  | [optional]
**update_by** | [**\OpenAPI\Client\Model\User**](User.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
