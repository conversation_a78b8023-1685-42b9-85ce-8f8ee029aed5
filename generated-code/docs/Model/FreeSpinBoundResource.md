# # FreeSpinBoundResource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**free_spin_id** | **int** | bigint |
**uuid** | **string** | string(32) |
**bonus_id** | **int** | bigint | [optional]
**bet** | **int** | bigint |
**total_win** | **int** | integer |
**count_left** | **int** | integer |
**is_active** | **bool** | boolean | [default to true]
**is_archived** | **bool** | boolean | [default to false]
**start_at** | **float** | datetime | [optional]
**expire_at** | **float** | datetime | [optional]
**canceled_at** | **float** | datetime | [optional]
**created_at** | **float** | datetime | [optional]
**updated_at** | **float** | datetime | [optional]
**freespin** | [**\OpenAPI\Client\Model\FreeSpinResource**](FreeSpinResource.md) |  | [optional]
**slot_id** | **int** |  | [optional]
**bonus** | [**\OpenAPI\Client\Model\BonusResource**](BonusResource.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
