# # BonusResource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** |  | [optional]
**weight** | **int** |  | [optional]
**name** | **string** | string(255) | [optional]
**bonus_name** | **object** |  | [optional]
**description** | **object** |  | [optional]
**condition** | **object** |  | [optional]
**type** | **string** |  | [optional] [default to 'wager']
**image** | **string** |  | [optional]
**max_bonuses** | **int[]** |  | [optional]
**max_transfers** | **int[]** |  | [optional]
**deposit_factors** | **int[]** |  | [optional]
**currency** | **string** | string(3) | [optional]
**wager** | **float** | decimal | [optional]
**min_deposit** | **int** | integer | [optional]
**min_factor** | **float** | float | [optional]
**min_bet** | **int** | bigint | [optional]
**active** | **bool** | boolean | [optional]
**casino** | **bool** | boolean | [optional]
**bets** | **bool** | boolean | [optional]
**crash** | **bool** | boolean | [optional] [default to false]
**total_transferred** | **int** | bigint | [optional]
**uses** | **int** | integer | [optional]
**transfers** | **int** | bigint | [optional]
**total_uses** | **int** | bigint | [optional]
**is_external** | **bool** | boolean | [optional] [default to false]
**is_promo** | **bool** | boolean | [optional] [default to false]
**from** | **\DateTime** | datetime | [optional]
**to** | **\DateTime** | datetime | [optional]
**duration** | **int** | bigint | [optional]
**bonus_data** | **object** |  | [optional]
**is_welcome** | **bool** | boolean | [optional]
**is_onetime** | **bool** | boolean | [optional]
**is_no_dep** | **bool** | boolean | [optional]
**is_deposit** | **bool** | boolean | [optional]
**is_free_spin_for_deposit** | **bool** | boolean | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**slot_providers** | **int[]** |  | [optional]
**slots** | **int[]** |  | [optional]
**freespin** | [**\OpenAPI\Client\Model\FreeSpin**](FreeSpin.md) |  | [optional]
**trigger_sessions** | [**\OpenAPI\Client\Model\BonusTriggerSession[]**](BonusTriggerSession.md) |  | [optional]
**max_real_balance** | **int** |  | [optional]
**genre_id** | **int** |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
