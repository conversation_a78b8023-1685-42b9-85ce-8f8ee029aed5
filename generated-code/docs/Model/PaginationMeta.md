# # PaginationMeta

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**current_page** | **int** | Current page | [optional] [default to 1]
**from** | **int** | From | [optional] [default to 1]
**last_page** | **int** | Last page | [optional] [default to 11]
**links** | [**\OpenAPI\Client\Model\PaginationLinksInner[]**](PaginationLinksInner.md) |  | [optional]
**path** | **string** | Path | [optional] [default to 'http://localhost:8000/api/bonuses']
**per_page** | **int** | Items per page | [optional] [default to 15]
**to** | **int** | Total pages | [optional]
**total** | **int** | Total items | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
