# # FreeSpin

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint |
**slot_id** | **int** | bigint |
**provider_id** | **int** | bigint |
**bonus_id** | **string** | string(255) | [optional]
**type** | **string** | string(255) | [default to 'manual']
**name** | **string** | string(50) |
**count** | **int** | integer |
**bet** | **int** | integer |
**bet_id** | **string** | string(255) | [optional]
**denomination** | **string** | string(255) | [optional]
**currency** | **string** | string(3) |
**status** | **string** | string(255) | [default to 'active']
**is_active** | **bool** | boolean | [default to false]
**author_id** | **int** | bigint |
**updated_by_author_id** | **int** | bigint | [optional]
**in_process** | **bool** | boolean | [default to false]
**start_at** | **\DateTime** | datetime | [optional]
**expired_at** | **\DateTime** | datetime | [optional]
**data** | **string** | json | [optional]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**uuid** | **string** | string(36) | [optional]
**bounds** | [**\OpenAPI\Client\Model\FreeSpinBound[]**](FreeSpinBound.md) |  | [optional]
**bonus** | [**\OpenAPI\Client\Model\Bonus**](Bonus.md) |  | [optional]
**author** | [**\OpenAPI\Client\Model\User**](User.md) |  | [optional]
**update_by** | [**\OpenAPI\Client\Model\User**](User.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
