# # PromoCodeResource

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** | bigint | [optional]
**client_id** | **int** | bigint | [optional]
**name** | **string** | string(255) | [optional]
**code** | **string** | string(255) | [optional]
**stream_id** | **int** | bigint | [optional]
**bonus_id** | **int** | bigint | [optional]
**description** | **string** | text | [optional]
**active** | **bool** | boolean | [optional] [default to false]
**uses** | **int** | integer | [optional]
**limit** | **int** | integer | [optional]
**start_at** | **int** | datetime | [optional]
**end_at** | **int** | datetime | [optional]
**condition** | **object** |  | [optional]
**is_alanbase** | **bool** | boolean | [optional] [default to false]
**created_at** | **\DateTime** | datetime | [optional]
**updated_at** | **\DateTime** | datetime | [optional]
**uuid** | **string** | string(36) | [optional]
**bonus** | [**\OpenAPI\Client\Model\BonusResource**](BonusResource.md) |  | [optional]

[[Back to Model list]](../../README.md#models) [[Back to API list]](../../README.md#endpoints) [[Back to README]](../../README.md)
