#!/bin/bash

# Remove previously generated code
sudo rm -fr ./generated-code/

# Generate code using OpenAPI generator
docker run --rm \
    -v $(pwd):/local \
    openapitools/openapi-generator-cli generate \
    -i /local/resources/swagger/openapi.json \
    -g php-laravel \
    -o /local/generated-code \
    -t /local/templates/php-laravel \
    -c /local/templates/php-laravel/config.json \
    --additional-properties=packageName=App \
    --skip-validate-spec

# Set proper permissions
sudo chmod 0755 -R ./generated-code/ 2>/dev/null || true

# Move Request classes from Model to Http/Requests
echo "Moving Request classes to Http/Requests..."

# Create Http/Requests directory
mkdir -p generated-code/Http/Requests

# Find Model classes that end with "Request" and move them to Http/Requests
for model_file in generated-code/Model/*Request.php; do
    if [ -f "$model_file" ]; then
        # Extract class name without path and extension
        class_name=$(basename "$model_file" .php)

        echo "Moving $class_name from Model to Http/Requests..."

        # Move the file to Http/Requests directory
        mv "$model_file" "generated-code/Http/Requests/"

        echo "Moved $class_name to Http/Requests/"
    fi
done

# Create Http/Resources directory
mkdir -p generated-code/Http/Resources

# Find Model classes that end with "Resource" and move them to Http/Resources
for model_file in generated-code/Model/*Resource.php; do
    if [ -f "$model_file" ]; then
        # Extract class name without path and extension
        class_name=$(basename "$model_file" .php)

        echo "Moving $class_name from Model to Http/Resources..."

        # Move the file to Http/Resources directory
        mv "$model_file" "generated-code/Http/Resources/"

        echo "Moved $class_name to Http/Resources/"
    fi
done

# Set proper permissions
sudo chmod 0755 -R ./generated-code/ 2>/dev/null || true
