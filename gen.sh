#!/bin/bash

# Remove previously generated code
sudo rm -fr ./.generated-code/

# Generate code using OpenAPI generator
docker run --rm \
    -v $(pwd):/local \
    openapitools/openapi-generator-cli generate \
    -i /local/resources/swagger/openapi.json \
    -g php-laravel \
    -o /local/generated-code \
    -t /local/templates/php-laravel \
    -c /local/templates/php-laravel/config.json \
    --additional-properties=packageName=App \
    --skip-validate-spec

# Create Request classes from Model classes that represent requestBody
echo "Creating Request classes in Http/Requests..."

# Create Http/Requests directory
mkdir -p generated-code/Http/Requests

# Find Model classes that end with "Request" and move them to Http/Requests
for model_file in generated-code/Model/*Request.php; do
    if [ -f "$model_file" ]; then
        # Extract class name without path and extension
        class_name=$(basename "$model_file" .php)
        
        echo "Moving $class_name to Http/Requests..."
        
        # Remove "Request" suffix from class name to avoid duplication
        clean_class_name=$(echo "$class_name" | sed 's/Request$//')
        request_file="generated-code/Http/Requests/${clean_class_name}Request.php"
        
        # Copy the model file to the Request location
        cp "$model_file" "$request_file"
        
        echo "Created $request_file"
    fi
done

# Set proper permissions
sudo chmod 0755 -R ./generated-code/ 2>/dev/null || true
