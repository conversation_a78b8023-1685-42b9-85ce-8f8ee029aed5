#!/bin/bash

# Remove previously generated code
sudo rm -fr ./.generated-code/

# Generate code using OpenAPI generator
docker run --rm \
    -v $(pwd):/local \
    openapitools/openapi-generator-cli generate \
    -i /local/resources/swagger/openapi.json \
    -g php-laravel \
    -o /local/generated-code \
    -t /local/templates/php-laravel \
    -c /local/templates/php-laravel/config.json \
    --additional-properties=packageName=App \
    --skip-validate-spec

# Create Request classes from Model classes that represent requestBody
echo "Creating Request classes in Http/Requests..."

# Create Http/Requests directory
mkdir -p generated-code/Http/Requests

# Find Model classes that end with "Request" and convert them to Request classes
for model_file in generated-code/Model/*Request.php; do
    if [ -f "$model_file" ]; then
        # Extract class name without path and extension
        class_name=$(basename "$model_file" .php)

        echo "Converting $class_name to Request class..."

        # Create Request class file
        request_file="generated-code/Http/Requests/${class_name}Request.php"

        # Copy the model file and modify it to be a Request class
        cp "$model_file" "$request_file"

        # Replace namespace and extend FormRequest instead of being a model
        sed -i 's/namespace OpenAPI\\Server\\Model;/namespace App\\Http\\Requests;/' "$request_file"
        sed -i '/use Crell\\Serde/d' "$request_file"
        sed -i '/use Illuminate\\Foundation\\Http\\FormRequest;/!{/^use /a\
use Illuminate\\Foundation\\Http\\FormRequest;
}' "$request_file"
        sed -i '/^#\[Serde/d' "$request_file"
        sed -i "s/class $class_name/class ${class_name}Request extends FormRequest/" "$request_file"

        # Add FormRequest methods
        sed -i '/^class /a\
{\
    /**\
     * Determine if the user is authorized to make this request.\
     */\
    public function authorize(): bool\
    {\
        return true;\
    }\
\
    /**\
     * Get the validation rules that apply to the request.\
     *\
     * @return array<string, \\Illuminate\\Contracts\\Validation\\ValidationRule|array<mixed>|string>\
     */\
    public function rules(): array\
    {\
        return [\
            // TODO: Add validation rules based on the model properties\
        ];\
    }' "$request_file"

        # Remove the constructor and other model-specific code
        sed -i '/public function __construct(/,/^    }/d' "$request_file"

        echo "Created $request_file"
    fi
done

# Set proper permissions
sudo chmod 0755 -R ./generated-code/ 2>/dev/null || true
