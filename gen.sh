#!/bin/bash

# Remove previously generated code
sudo rm -fr ./.generated-code/

# Generate code using OpenAPI generator
docker run --rm \
    -v $(pwd):/local \
    openapitools/openapi-generator-cli generate \
    -i /local/resources/swagger/openapi.json \
    -g php-laravel \
    -o /local/generated-code \
    -t /local/templates/php-laravel \
    -c /local/templates/php-laravel/config.json \
    --additional-properties=packageName=App,requestPackage=Http\\Requests \
    --skip-validate-spec

# Clean up and create proper Request classes
echo "Creating Request classes for requestBody models..."

# Remove the generic template file
rm -f generated-code/Http/Requests/{{classname}}Request.php

# Create Request classes for models that end with "Request" (these represent requestBody)
for model_file in generated-code/Model/*Request.php; do
    if [ -f "$model_file" ]; then
        # Extract class name without path and extension
        class_name=$(basename "$model_file" .php)

        echo "Creating Request class for $class_name..."

        # Create Request class file
        request_file="generated-code/Http/Requests/${class_name}Request.php"

        # Create a proper Request class
        cat > "$request_file" << 'EOF'
<?php declare(strict_types=1);

/**
 * Laravel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * CLASS_NAME_PLACEHOLDERRequest
 */
class CLASS_NAME_PLACEHOLDERRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // TODO: Add validation rules based on the corresponding Model
        ];
    }
}
EOF

        # Replace placeholder with actual class name
        sed -i "s/CLASS_NAME_PLACEHOLDER/$class_name/g" "$request_file"
    fi
done

# Set proper permissions
sudo chmod 0755 -R ./generated-code/ 2>/dev/null || true
