<?php

/**
 * <PERSON><PERSON><PERSON> to convert Model classes ending with "Request" to Laravel FormRequest classes
 */

$requestsDir = 'generated-code/Http/Requests';
$modelsDir = 'generated-code/Model';

// Find all Model files ending with "Request"
$modelFiles = glob($modelsDir . '/*Request.php');

foreach ($modelFiles as $modelFile) {
    $className = basename($modelFile, '.php');
    $cleanClassName = preg_replace('/Request$/', '', $className);
    $requestClassName = $cleanClassName . 'Request';
    
    echo "Converting $className to $requestClassName...\n";
    
    // Read the model file
    $content = file_get_contents($modelFile);
    
    // Create Request class content
    $requestContent = "<?php declare(strict_types=1);

/**
 * Laravel
 * Documentation for the Application API
 * PHP version 8.1
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI-Generator
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * $requestClassName
 */
class $requestClassName extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // TODO: Add validation rules based on the $className model
        ];
    }
}
";
    
    // Write the Request class
    $requestFile = $requestsDir . '/' . $requestClassName . '.php';
    file_put_contents($requestFile, $requestContent);
    
    echo "Created $requestFile\n";
}

echo "Conversion completed!\n";
